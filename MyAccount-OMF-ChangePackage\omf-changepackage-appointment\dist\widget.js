/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("react"), require("react-redux"), require("omf-changepackage-components"), require("bwtk"), require("redux"), require("redux-actions"), require("redux-observable"), require("rxjs"), require("react-intl"));
	else if(typeof define === 'function' && define.amd)
		define(["react", "react-redux", "omf-changepackage-components", "bwtk", "redux", "redux-actions", "redux-observable", "rxjs", "react-intl"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("react"), require("react-redux"), require("omf-changepackage-components"), require("bwtk"), require("redux"), require("redux-actions"), require("redux-observable"), require("rxjs"), require("react-intl")) : factory(root["React"], root["ReactRedux"], root["OMFChangepackageComponents"], root["bwtk"], root["Redux"], root["ReduxActions"], root["ReduxObservable"], root["rxjs"], root["ReactIntl"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, function(__WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_redux__, __WEBPACK_EXTERNAL_MODULE_omf_changepackage_components__, __WEBPACK_EXTERNAL_MODULE_bwtk__, __WEBPACK_EXTERNAL_MODULE_redux__, __WEBPACK_EXTERNAL_MODULE_redux_actions__, __WEBPACK_EXTERNAL_MODULE_redux_observable__, __WEBPACK_EXTERNAL_MODULE_rxjs__, __WEBPACK_EXTERNAL_MODULE_react_intl__) {
return /******/ (function() { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "../src/Widget.tsx":
/*!**************************************!*\
  !*** ../src/Widget.tsx + 40 modules ***!
  \**************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("{// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ Widget; }\n});\n\n// NAMESPACE OBJECT: ../src/store/Actions.ts\nvar Actions_namespaceObject = {};\n__webpack_require__.r(Actions_namespaceObject);\n__webpack_require__.d(Actions_namespaceObject, {\n  contactInformation: function() { return contactInformation; },\n  getAppointment: function() { return getAppointment; },\n  getOderDetails: function() { return getOderDetails; },\n  initSlickSlider: function() { return initSlickSlider; },\n  setAdditionalDetails: function() { return setAdditionalDetails; },\n  setAppointment: function() { return setAppointment; },\n  setAvailableDates: function() { return setAvailableDates; },\n  setDuration: function() { return setDuration; },\n  setForErrors: function() { return setForErrors; },\n  setInstallationAddress: function() { return setInstallationAddress; },\n  setIsInstallationRequired: function() { return setIsInstallationRequired; }\n});\n\n;// ./tslib/tslib.es6.mjs\n/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nvar __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nfunction __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nfunction __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nfunction __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nfunction __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nfunction __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nfunction __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nfunction __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nfunction __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nfunction __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nvar __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nfunction __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nfunction __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nfunction __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nfunction __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nfunction __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nfunction __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nfunction __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nfunction __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nfunction __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nfunction __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nfunction __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nfunction __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nfunction __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nfunction __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nfunction __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\n/* harmony default export */ var tslib_es6 = ({\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n});\n\n// EXTERNAL MODULE: external {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}\nvar external_root_React_commonjs2_react_commonjs_react_amd_react_ = __webpack_require__(\"react\");\n// EXTERNAL MODULE: external {\"root\":\"ReactRedux\",\"commonjs2\":\"react-redux\",\"commonjs\":\"react-redux\",\"amd\":\"react-redux\"}\nvar external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_ = __webpack_require__(\"react-redux\");\n// EXTERNAL MODULE: external {\"root\":\"OMFChangepackageComponents\",\"commonjs2\":\"omf-changepackage-components\",\"commonjs\":\"omf-changepackage-components\",\"amd\":\"omf-changepackage-components\"}\nvar external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_ = __webpack_require__(\"omf-changepackage-components\");\n// EXTERNAL MODULE: external {\"root\":\"bwtk\",\"commonjs2\":\"bwtk\",\"commonjs\":\"bwtk\",\"amd\":\"bwtk\"}\nvar external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_ = __webpack_require__(\"bwtk\");\n// EXTERNAL MODULE: external {\"root\":\"Redux\",\"commonjs2\":\"redux\",\"commonjs\":\"redux\",\"amd\":\"redux\"}\nvar external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_ = __webpack_require__(\"redux\");\n// EXTERNAL MODULE: external {\"root\":\"ReduxActions\",\"commonjs2\":\"redux-actions\",\"commonjs\":\"redux-actions\",\"amd\":\"redux-actions\"}\nvar external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_ = __webpack_require__(\"redux-actions\");\n// EXTERNAL MODULE: external {\"root\":\"ReduxObservable\",\"commonjs2\":\"redux-observable\",\"commonjs\":\"redux-observable\",\"amd\":\"redux-observable\"}\nvar external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_ = __webpack_require__(\"redux-observable\");\n;// ../src/store/Actions.ts\n\nvar getOderDetails = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"GET_ORDER_DETAILS\");\nvar getAppointment = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"GET_APPOINTMENT\");\nvar setAppointment = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_APPOINTMENT\");\nvar setAvailableDates = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_AVAIALBLE_DATES\");\nvar contactInformation = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_CONTACT_INFO\");\nvar setDuration = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_DURATION\");\nvar setInstallationAddress = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_INSTALLATION_ADDRESS\");\nvar setAdditionalDetails = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_ADDITIONAL_DETAILS\");\nvar setIsInstallationRequired = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_INSTALLATION_REQUIRED\");\nvar setForErrors = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_FORM_ERRORS\");\nvar initSlickSlider = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"INIT_SLICK_SLIDER\");\n\n// EXTERNAL MODULE: external {\"root\":\"rxjs\",\"commonjs2\":\"rxjs\",\"commonjs\":\"rxjs\",\"amd\":\"rxjs\"}\nvar external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_ = __webpack_require__(\"rxjs\");\n;// ../src/Config.ts\n\n\nvar BaseConfig = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseConfig, configProperty = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.configProperty;\nvar Config = (function (_super) {\n    __extends(Config, _super);\n    function Config() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"headers\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"environmentVariables\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"mockdata\", void 0);\n    __decorate([\n        configProperty({ base: \"http://127.0.0.1:8881\", orderDetailsAPI: \"/\", appointmentAPI: \"/\", orderSubmitAPI: \"/\" }),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"api\", void 0);\n    Config = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], Config);\n    return Config;\n}(BaseConfig));\n\n\n;// ../src/Client.ts\n\n\n\n\nvar Client = (function (_super) {\n    __extends(Client, _super);\n    function Client(ajaxClient, config) {\n        return _super.call(this, ajaxClient, config) || this;\n    }\n    Client = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.AjaxServices, Config])\n    ], Client);\n    return Client;\n}(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.BaseClient));\n\n\n;// ../src/models/Store.ts\n\n\n;// ../src/models/App.ts\n\n\n;// ../src/models/Widget.ts\n\n\n;// ../src/models/Appointment.ts\nvar Request = {\n    availableDates: null,\n    duration: \"\",\n    installationAddress: {\n        address1: \"\",\n        address2: \"\",\n        city: \"\",\n        province: \"\",\n        postalCode: \"\",\n        apartmentType: \"\",\n        apartmentNumber: \"\"\n    },\n    contactInformation: {\n        preferredContactMethod: \"\",\n        primaryPhone: {\n            phoneNumber: \"\",\n            phoneExtension: \"\"\n        },\n        mobileNumber: null,\n        additionalPhone: {\n            phoneNumber: \"\",\n            phoneExtension: \"\"\n        },\n        textMessage: \"\",\n        email: \"\"\n    },\n    additionalDetails: {\n        apartment: \"\",\n        entryCode: \"\",\n        specialInstructions: \"\",\n        superintendantName: \"\",\n        superintendantPhone: \"\",\n        informedSuperintendant: null\n    },\n    isInstallationRequired: null\n};\nvar MapRequestData = (function () {\n    function MapRequestData() {\n    }\n    MapRequestData.create = function (payload, request, store) {\n        request.installationAddress.address1 = store.installationAddress && store.installationAddress.address1 ? store.installationAddress.address1 : \"\";\n        request.installationAddress.address2 = store.installationAddress && store.installationAddress.address2 ? store.installationAddress.address2 : \"\";\n        request.installationAddress.city = store.installationAddress && store.installationAddress.city ? store.installationAddress.city : \"\";\n        request.installationAddress.postalCode = store.installationAddress && store.installationAddress.postalCode ? store.installationAddress.postalCode : \"\";\n        request.installationAddress.province = store.installationAddress && store.installationAddress.province ? store.installationAddress.province : \"\";\n        request.installationAddress.apartmentType = store.installationAddress && store.installationAddress.province ? store.installationAddress.apartmentType : \"\";\n        request.installationAddress.apartmentNumber = store.installationAddress && store.installationAddress.province ? store.installationAddress.apartmentNumber : \"\";\n        request.isInstallationRequired = store.isInstallationRequired;\n        request.duration = store.duration;\n        request.contactInformation.primaryPhone.phoneNumber = payload.Phone_LABEL ? payload.Phone_LABEL : \"\";\n        request.contactInformation.primaryPhone.phoneExtension = payload.Phone_EXT ? payload.Phone_EXT : \"\";\n        request.contactInformation.additionalPhone.phoneNumber = payload.ADDITIONAL_PHONE_NUMBER;\n        request.contactInformation.additionalPhone.phoneExtension = payload.ADDITIONAL_PHONE_EXT;\n        request.contactInformation.preferredContactMethod = payload.PREFERED_METHOD_OF_CONTACT;\n        request.contactInformation.email = payload.Email_LABEL ? payload.Email_LABEL : \"\";\n        request.contactInformation.textMessage = payload.TextMessage_LABEL ? payload.TextMessage_LABEL : \"\";\n        request.availableDates = updateAvailableDates(store.availableDates, JSON.parse(payload.dateAndTime));\n        request.additionalDetails.apartment = payload.APPARTMENT;\n        request.additionalDetails.entryCode = payload.ENTRY_CODE;\n        request.additionalDetails.informedSuperintendant = payload.INFORMED_SUPERINTENDANT;\n        request.additionalDetails.specialInstructions = payload.SPECIAL_INSTRUCTIONS;\n        request.additionalDetails.superintendantName = payload.SUPERINTENDANT_NAME;\n        request.additionalDetails.superintendantPhone = payload.SUPERINTENDANT_PHONE;\n        return request;\n    };\n    return MapRequestData;\n}());\n\nfunction updateAvailableDates(dates, selectedDate) {\n    dates === null || dates === void 0 ? void 0 : dates.forEach(function (date) {\n        date.timeSlots.forEach(function (time) { return time.isSelected = false; });\n    });\n    dates === null || dates === void 0 ? void 0 : dates.forEach(function (date) { return (date.timeSlots.forEach(function (time) { return time.isSelected = (date.date === selectedDate.date && selectedDate.timeSlots.map(function (selectedTime) { return selectedTime.intervalType === time.intervalType; })) ? true : false; })); });\n    return dates;\n}\n\n;// ../src/models/index.ts\n\n\n\n\n\n;// ../src/store/Epics/Appointment.ts\n\n\n\n\n\n\n\n\n\nvar errorOccured = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.errorOccured, setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus, setProductConfigurationTotal = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setProductConfigurationTotal, broadcastUpdate = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate, historyGo = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyGo, clearCachedState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.clearCachedState, omniPageLoaded = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageLoaded;\nvar AppointmentEpics = (function () {\n    function AppointmentEpics(client, config) {\n        this.client = client;\n        this.config = config;\n        this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n    }\n    AppointmentEpics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.appointmentEpic, this.submitAppointmentEpic);\n    };\n    Object.defineProperty(AppointmentEpics.prototype, \"appointmentEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(getAppointment.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function () { return _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat)((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING)), _this.client.get(_this.config.api.appointmentAPI).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                        var data = _a.data;\n                        return [\n                            setAvailableDates(data.appointment.availableDates),\n                            setDuration(data.appointment.duration),\n                            setInstallationAddress(data.appointment.installationAddress),\n                            contactInformation(data.appointment.contactInformation),\n                            setAdditionalDetails(data.appointment.additionalDetails),\n                            setIsInstallationRequired(data.appointment.isInstallationRequired),\n                            broadcastUpdate(setProductConfigurationTotal((0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(data, \"productConfigurationTotal\"))),\n                            setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.RENDERED),\n                            omniPageLoaded()\n                        ];\n                    })));\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(errorOccured(new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Models.ErrorHandler(\"getAppointment\", error))); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AppointmentEpics.prototype, \"submitAppointmentEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, store) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(setAppointment.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function () { return _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                    var payload = _a.payload;\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat)((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING)), _this.client.put(_this.config.api.appointmentAPI, MapRequestData.create(payload, Request, store.getState())).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.from)([\n                        broadcastUpdate(historyGo(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.REVIEW)),\n                        clearCachedState([external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.REVIEW])\n                    ]); })));\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(errorOccured(new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Models.ErrorHandler(\"getAppointment\", error))); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AppointmentEpics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, Config])\n    ], AppointmentEpics);\n    return AppointmentEpics;\n}());\n\n\n;// ../src/store/Epics/Omniture.ts\n\n\n\n\n\nvar Omniture_omniPageLoaded = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageLoaded;\nvar OmnitureEpics = (function () {\n    function OmnitureEpics() {\n        this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n    }\n    OmnitureEpics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.pageLoadedEpic);\n    };\n    Object.defineProperty(OmnitureEpics.prototype, \"pageLoadedEpic\", {\n        get: function () {\n            return function (action$, store) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(Omniture_omniPageLoaded.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var omniture = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture();\n                    var currentFlowType = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getFlowType();\n                    var s_oSS3, s_oSS2;\n                    switch (currentFlowType) {\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.INTERNET:\n                            s_oSS2 = \"Internet\";\n                            s_oSS3 = \"Change package\";\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.TV:\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.ADDTV:\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE:\n                            s_oSS2 = \"Bundle\";\n                            s_oSS3 = \"Add Tv\";\n                            break;\n                    }\n                    omniture.trackPage({\n                        id: \"AppointmentPage\",\n                        s_oSS1: \"~\",\n                        s_oSS2: s_oSS2 ? s_oSS2 : \"~\",\n                        s_oSS3: s_oSS3 ? s_oSS3 : \"Change package\",\n                        s_oPGN: \"Installation\",\n                        s_oPLE: {\n                            type: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.EMessageType.Warning,\n                            content: {\n                                ref: \"IstallationMessageBanner\"\n                            }\n                        }\n                    });\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)([]);\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)([]); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OmnitureEpics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], OmnitureEpics);\n    return OmnitureEpics;\n}());\n\n\n;// ../src/store/Epics.ts\n\n\n\n\n\n\n\n\nvar Epics_setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus, Epics_broadcastUpdate = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate, setAppointmentVisited = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setAppointmentVisited;\nvar Epics = (function () {\n    function Epics(omnitureEpics, appointmentEpics) {\n        this.omnitureEpics = omnitureEpics;\n        this.appointmentEpics = appointmentEpics;\n    }\n    Epics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.onWidgetStatusEpic);\n    };\n    Object.defineProperty(Epics.prototype, \"onWidgetStatusEpic\", {\n        get: function () {\n            return function (action$) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(Epics_setWidgetStatus.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (_a) {\n                    var payload = _a.payload;\n                    return payload === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(Epics_broadcastUpdate(setAppointmentVisited()), getAppointment()); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Epics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [OmnitureEpics,\n            AppointmentEpics])\n    ], Epics);\n    return Epics;\n}());\n\n\n;// ../src/Localization.ts\n\n\nvar BaseLocalization = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseLocalization;\nvar SOURCE_WIDGET_ID = \"omf-changepackage-appointment\";\nvar Localization = (function (_super) {\n    __extends(Localization, _super);\n    function Localization() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Localization_1 = Localization;\n    Localization.getLocalizedString = function (id) {\n        Localization_1.Instance = Localization_1.Instance ||\n            external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ServiceLocator\n                .instance\n                .getService(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonServices.Localization);\n        var instance = Localization_1.Instance;\n        return instance ? instance.getLocalizedString(SOURCE_WIDGET_ID, id, instance.locale) : id;\n    };\n    var Localization_1;\n    Localization.Instance = null;\n    Localization = Localization_1 = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], Localization);\n    return Localization;\n}(BaseLocalization));\n\n\n;// ../src/store/Store.ts\n\n\n\n\n\n\n\n\n\n\nvar BaseStore = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseStore, actionsToComputedPropertyName = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.actionsToComputedPropertyName;\nvar _a = actionsToComputedPropertyName(Actions_namespaceObject), Store_setAvailableDates = _a.setAvailableDates, Store_setDuration = _a.setDuration, Store_setInstallationAddress = _a.setInstallationAddress, Store_contactInformation = _a.contactInformation, Store_setAdditionalDetails = _a.setAdditionalDetails, Store_setIsInstallationRequired = _a.setIsInstallationRequired;\nvar Store = (function (_super) {\n    __extends(Store, _super);\n    function Store(client, store, epics, localization) {\n        var _this = _super.call(this, store) || this;\n        _this.client = client;\n        _this.epics = epics;\n        _this.localization = localization;\n        return _this;\n    }\n    Object.defineProperty(Store.prototype, \"reducer\", {\n        get: function () {\n            var _a, _b, _c, _d, _e, _f;\n            return (0,external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_.combineReducers)(__assign(__assign(__assign(__assign({}, external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetBaseLifecycle(this.localization)), external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetLightboxes()), external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetRestrictions()), { availableDates: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_a = {},\n                    _a[Store_setAvailableDates] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _a), null), duration: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_b = {},\n                    _b[Store_setDuration] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _b), null), installationAddress: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_c = {},\n                    _c[Store_setInstallationAddress] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _c), null), contactInformation: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_d = {},\n                    _d[Store_contactInformation] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _d), null), additionalDetails: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_e = {},\n                    _e[Store_setAdditionalDetails] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _e), null), isInstallationRequired: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_f = {},\n                    _f[Store_setIsInstallationRequired] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _f), false) }));\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Store.prototype, \"middlewares\", {\n        get: function () {\n            return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.epics.omnitureEpics.combineEpics(), this.epics.appointmentEpics.combineEpics(), this.epics.combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ModalEpics().combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.RestricitonsEpics(this.client, \"APPOINTMENT_RESTRICTION_MODAL\").combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.LifecycleEpics().combineEpics());\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Store = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Store, Epics, Localization])\n    ], Store);\n    return Store;\n}(BaseStore));\n\n\n;// ../src/store/index.ts\n\n\n\n;// ./react-hook-form/dist/react-hook-form.es.js\n\n\n\nconst VALIDATION_MODE = {\r\n    onBlur: 'onBlur',\r\n    onChange: 'onChange',\r\n    onSubmit: 'onSubmit',\r\n};\r\nconst RADIO_INPUT = 'radio';\r\nconst FILE_INPUT = 'file';\r\nconst VALUE = 'value';\r\nconst UNDEFINED = 'undefined';\r\nconst EVENTS = {\r\n    BLUR: 'blur',\r\n    CHANGE: 'change',\r\n    INPUT: 'input',\r\n};\r\nconst INPUT_VALIDATION_RULES = {\r\n    max: 'max',\r\n    min: 'min',\r\n    maxLength: 'maxLength',\r\n    minLength: 'minLength',\r\n    pattern: 'pattern',\r\n    required: 'required',\r\n    validate: 'validate',\r\n};\r\nconst REGEX_IS_DEEP_PROP = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/;\r\nconst REGEX_IS_PLAIN_PROP = /^\\w*$/;\r\nconst REGEX_PROP_NAME = /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g;\r\nconst REGEX_ESCAPE_CHAR = /\\\\(\\\\)?/g;\n\nfunction attachEventListeners({ field, handleChange, isRadioOrCheckbox, }) {\r\n    const { ref } = field;\r\n    if (ref.addEventListener) {\r\n        ref.addEventListener(isRadioOrCheckbox ? EVENTS.CHANGE : EVENTS.INPUT, handleChange);\r\n        ref.addEventListener(EVENTS.BLUR, handleChange);\r\n    }\r\n}\n\nvar isUndefined = (val) => val === undefined;\n\nvar isNullOrUndefined = (value) => value === null || isUndefined(value);\n\nvar isArray = (value) => Array.isArray(value);\n\nconst isObjectType = (value) => typeof value === 'object';\r\nvar isObject = (value) => !isNullOrUndefined(value) && !isArray(value) && isObjectType(value);\n\nconst isKey = (value) => !isArray(value) &&\r\n    (REGEX_IS_PLAIN_PROP.test(value) || !REGEX_IS_DEEP_PROP.test(value));\r\nconst stringToPath = (string) => {\r\n    const result = [];\r\n    string.replace(REGEX_PROP_NAME, (match, number, quote, string) => {\r\n        result.push(quote ? string.replace(REGEX_ESCAPE_CHAR, '$1') : number || match);\r\n    });\r\n    return result;\r\n};\r\nfunction set(object, path, value) {\r\n    let index = -1;\r\n    const tempPath = isKey(path) ? [path] : stringToPath(path);\r\n    const length = tempPath.length;\r\n    const lastIndex = length - 1;\r\n    while (++index < length) {\r\n        const key = tempPath[index];\r\n        let newValue = value;\r\n        if (index !== lastIndex) {\r\n            const objValue = object[key];\r\n            newValue =\r\n                isObject(objValue) || isArray(objValue)\r\n                    ? objValue\r\n                    : !isNaN(tempPath[index + 1])\r\n                        ? []\r\n                        : {};\r\n        }\r\n        object[key] = newValue;\r\n        object = object[key];\r\n    }\r\n    return object;\r\n}\n\nvar transformToNestObject = (data) => Object.entries(data).reduce((previous, [key, value]) => {\r\n    if (REGEX_IS_DEEP_PROP.test(key)) {\r\n        set(previous, key, value);\r\n        return previous;\r\n    }\r\n    return Object.assign(Object.assign({}, previous), { [key]: value });\r\n}, {});\n\nvar removeAllEventListeners = (ref, validateWithStateUpdate) => {\r\n    if (ref.removeEventListener) {\r\n        ref.removeEventListener(EVENTS.INPUT, validateWithStateUpdate);\r\n        ref.removeEventListener(EVENTS.CHANGE, validateWithStateUpdate);\r\n        ref.removeEventListener(EVENTS.BLUR, validateWithStateUpdate);\r\n    }\r\n};\n\nvar isRadioInput = (type) => type === RADIO_INPUT;\n\nvar isCheckBoxInput = (type) => type === 'checkbox';\n\nfunction isDetached(element) {\r\n    if (!element) {\r\n        return true;\r\n    }\r\n    if (!(element instanceof HTMLElement) ||\r\n        element.nodeType === Node.DOCUMENT_NODE) {\r\n        return false;\r\n    }\r\n    return isDetached(element.parentNode);\r\n}\n\nfunction findRemovedFieldAndRemoveListener(fields, handleChange, field, forceDelete) {\r\n    if (!field) {\r\n        return;\r\n    }\r\n    const { ref, ref: { name, type }, mutationWatcher, } = field;\r\n    if (!type) {\r\n        return;\r\n    }\r\n    const fieldValue = fields[name];\r\n    if ((isRadioInput(type) || isCheckBoxInput(type)) && fieldValue) {\r\n        const { options } = fieldValue;\r\n        if (isArray(options) && options.length) {\r\n            options.forEach(({ ref }, index) => {\r\n                if ((ref && isDetached(ref)) || forceDelete) {\r\n                    const mutationWatcher = ref.mutationWatcher;\r\n                    removeAllEventListeners(ref, handleChange);\r\n                    if (mutationWatcher) {\r\n                        mutationWatcher.disconnect();\r\n                    }\r\n                    options.splice(index, 1);\r\n                }\r\n            });\r\n            if (options && !options.length) {\r\n                delete fields[name];\r\n            }\r\n        }\r\n        else {\r\n            delete fields[name];\r\n        }\r\n    }\r\n    else if (isDetached(ref) || forceDelete) {\r\n        removeAllEventListeners(ref, handleChange);\r\n        if (mutationWatcher) {\r\n            mutationWatcher.disconnect();\r\n        }\r\n        delete fields[name];\r\n    }\r\n}\n\nconst defaultReturn = {\r\n    isValid: false,\r\n    value: '',\r\n};\r\nvar getRadioValue = (options) => isArray(options)\r\n    ? options.reduce((previous, { ref: { checked, value } }) => checked\r\n        ? {\r\n            isValid: true,\r\n            value,\r\n        }\r\n        : previous, defaultReturn)\r\n    : defaultReturn;\n\nvar getMultipleSelectValue = (options) => [...options]\r\n    .filter(({ selected }) => selected)\r\n    .map(({ value }) => value);\n\nvar isFileInput = (type) => type === FILE_INPUT;\n\nvar isMultipleSelect = (type) => type === 'select-multiple';\n\nvar isEmptyString = (value) => value === '';\n\nconst defaultResult = {\r\n    value: false,\r\n    isValid: false,\r\n};\r\nconst validResult = { value: true, isValid: true };\r\nvar getCheckboxValue = (options) => {\r\n    if (isArray(options)) {\r\n        if (options.length > 1) {\r\n            const values = options\r\n                .filter(({ ref: { checked } }) => checked)\r\n                .map(({ ref: { value } }) => value);\r\n            return { value: values, isValid: !!values.length };\r\n        }\r\n        const { checked, value, attributes } = options[0].ref;\r\n        return checked\r\n            ? attributes && !isUndefined(attributes.value)\r\n                ? isUndefined(value) || isEmptyString(value)\r\n                    ? validResult\r\n                    : { value: value, isValid: true }\r\n                : validResult\r\n            : defaultResult;\r\n    }\r\n    return defaultResult;\r\n};\n\nfunction getFieldValue(fields, ref) {\r\n    const { type, name, options, value, files } = ref;\r\n    const field = fields[name];\r\n    if (isFileInput(type)) {\r\n        return files;\r\n    }\r\n    if (isRadioInput(type)) {\r\n        return field ? getRadioValue(field.options).value : '';\r\n    }\r\n    if (isMultipleSelect(type)) {\r\n        return getMultipleSelectValue(options);\r\n    }\r\n    if (isCheckBoxInput(type)) {\r\n        return field ? getCheckboxValue(field.options).value : false;\r\n    }\r\n    return value;\r\n}\n\nvar getFieldsValues = (fields) => Object.values(fields).reduce((previous, { ref, ref: { name } }) => (Object.assign(Object.assign({}, previous), { [name]: getFieldValue(fields, ref) })), {});\n\nvar isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;\n\nvar isSameError = (error, type, message) => isObject(error) && error.type === type && error.message === message;\n\nvar get = (obj, path, defaultValue) => {\r\n    const result = path\r\n        .split(/[,[\\].]+?/)\r\n        .filter(Boolean)\r\n        .reduce((result, key) => (isNullOrUndefined(result) ? result : result[key]), obj);\r\n    return isUndefined(result) || result === obj\r\n        ? obj[path] || defaultValue\r\n        : result;\r\n};\n\nfunction shouldUpdateWithError({ errors, name, error, validFields, fieldsWithValidation, }) {\r\n    const isFieldValid = isEmptyObject(error);\r\n    const isFormValid = isEmptyObject(errors);\r\n    const currentFieldError = get(error, name);\r\n    const existFieldError = get(errors, name);\r\n    if ((isFieldValid && validFields.has(name)) ||\r\n        (existFieldError && existFieldError.isManual)) {\r\n        return false;\r\n    }\r\n    if (isFormValid !== isFieldValid ||\r\n        (!isFormValid && !existFieldError) ||\r\n        (isFieldValid && fieldsWithValidation.has(name) && !validFields.has(name))) {\r\n        return true;\r\n    }\r\n    return (currentFieldError &&\r\n        !isSameError(existFieldError, currentFieldError.type, currentFieldError.message));\r\n}\n\nvar isRegex = (value) => value instanceof RegExp;\n\nvar getValueAndMessage = (validationData) => {\r\n    const isPureObject = isObject(validationData) && !isRegex(validationData);\r\n    return {\r\n        value: isPureObject\r\n            ? validationData.value\r\n            : validationData,\r\n        message: isPureObject\r\n            ? validationData.message\r\n            : '',\r\n    };\r\n};\n\nvar isString = (value) => typeof value === 'string';\n\nvar isFunction = (value) => typeof value === 'function';\n\nvar isBoolean = (value) => typeof value === 'boolean';\n\nfunction getValidateError(result, ref, type = 'validate') {\r\n    const isStringValue = isString(result);\r\n    if (isStringValue || (isBoolean(result) && !result)) {\r\n        const message = isStringValue ? result : '';\r\n        return {\r\n            type,\r\n            message,\r\n            ref,\r\n        };\r\n    }\r\n}\n\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => {\r\n    if (!validateAllFieldCriteria) {\r\n        return {};\r\n    }\r\n    const error = errors[name];\r\n    return Object.assign(Object.assign({}, error), { types: Object.assign(Object.assign({}, (error && error.types ? error.types : {})), { [type]: message || true }) });\r\n};\n\nvar validateField = async (fieldsRef, validateAllFieldCriteria, { ref, ref: { type, value, name, valueAsNumber, valueAsDate }, options, required, maxLength, minLength, min, max, pattern, validate, }) => {\r\n    const fields = fieldsRef.current;\r\n    const error = {};\r\n    const isRadio = isRadioInput(type);\r\n    const isCheckBox = isCheckBoxInput(type);\r\n    const isRadioOrCheckbox = isRadio || isCheckBox;\r\n    const isEmpty = isEmptyString(value);\r\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\r\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\r\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\r\n        error[name] = Object.assign({ type: exceedMax ? maxType : minType, message,\r\n            ref }, (exceedMax\r\n            ? appendErrorsCurry(maxType, message)\r\n            : appendErrorsCurry(minType, message)));\r\n        if (!validateAllFieldCriteria) {\r\n            return error;\r\n        }\r\n    };\r\n    if (required &&\r\n        ((!isRadio && !isCheckBox && (isEmpty || isNullOrUndefined(value))) ||\r\n            (isBoolean(value) && !value) ||\r\n            (isCheckBox && !getCheckboxValue(options).isValid) ||\r\n            (isRadio && !getRadioValue(options).isValid))) {\r\n        const message = isString(required)\r\n            ? required\r\n            : getValueAndMessage(required).message;\r\n        error[name] = Object.assign({ type: INPUT_VALIDATION_RULES.required, message, ref: isRadioOrCheckbox ? fields[name].options[0].ref : ref }, appendErrorsCurry(INPUT_VALIDATION_RULES.required, message));\r\n        if (!validateAllFieldCriteria) {\r\n            return error;\r\n        }\r\n    }\r\n    if (!isNullOrUndefined(min) || !isNullOrUndefined(max)) {\r\n        let exceedMax;\r\n        let exceedMin;\r\n        const { value: maxValue, message: maxMessage } = getValueAndMessage(max);\r\n        const { value: minValue, message: minMessage } = getValueAndMessage(min);\r\n        if (type === 'number' || (!type && !isNaN(value))) {\r\n            const valueNumber = valueAsNumber || parseFloat(value);\r\n            if (!isNullOrUndefined(maxValue)) {\r\n                exceedMax = valueNumber > maxValue;\r\n            }\r\n            if (!isNullOrUndefined(minValue)) {\r\n                exceedMin = valueNumber < minValue;\r\n            }\r\n        }\r\n        else {\r\n            const valueDate = valueAsDate || new Date(value);\r\n            if (isString(maxValue)) {\r\n                exceedMax = valueDate > new Date(maxValue);\r\n            }\r\n            if (isString(minValue)) {\r\n                exceedMin = valueDate < new Date(minValue);\r\n            }\r\n        }\r\n        if (exceedMax || exceedMin) {\r\n            getMinMaxMessage(!!exceedMax, maxMessage, minMessage, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\r\n            if (!validateAllFieldCriteria) {\r\n                return error;\r\n            }\r\n        }\r\n    }\r\n    if (isString(value) && !isEmpty && (maxLength || minLength)) {\r\n        const { value: maxLengthValue, message: maxLengthMessage, } = getValueAndMessage(maxLength);\r\n        const { value: minLengthValue, message: minLengthMessage, } = getValueAndMessage(minLength);\r\n        const inputLength = value.toString().length;\r\n        const exceedMax = maxLength && inputLength > maxLengthValue;\r\n        const exceedMin = minLength && inputLength < minLengthValue;\r\n        if (exceedMax || exceedMin) {\r\n            getMinMaxMessage(!!exceedMax, maxLengthMessage, minLengthMessage);\r\n            if (!validateAllFieldCriteria) {\r\n                return error;\r\n            }\r\n        }\r\n    }\r\n    if (pattern && !isEmpty) {\r\n        const { value: patternValue, message: patternMessage } = getValueAndMessage(pattern);\r\n        if (isRegex(patternValue) && !patternValue.test(value)) {\r\n            error[name] = Object.assign({ type: INPUT_VALIDATION_RULES.pattern, message: patternMessage, ref }, appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, patternMessage));\r\n            if (!validateAllFieldCriteria) {\r\n                return error;\r\n            }\r\n        }\r\n    }\r\n    if (validate) {\r\n        const fieldValue = getFieldValue(fields, ref);\r\n        const validateRef = isRadioOrCheckbox && options ? options[0].ref : ref;\r\n        if (isFunction(validate)) {\r\n            const result = await validate(fieldValue);\r\n            const validateError = getValidateError(result, validateRef);\r\n            if (validateError) {\r\n                error[name] = Object.assign(Object.assign({}, validateError), appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message));\r\n                if (!validateAllFieldCriteria) {\r\n                    return error;\r\n                }\r\n            }\r\n        }\r\n        else if (isObject(validate)) {\r\n            const validateFunctions = Object.entries(validate);\r\n            const validationResult = await new Promise((resolve) => {\r\n                validateFunctions.reduce(async (previous, [key, validate], index) => {\r\n                    if ((!isEmptyObject(await previous) && !validateAllFieldCriteria) ||\r\n                        !isFunction(validate)) {\r\n                        return resolve(previous);\r\n                    }\r\n                    let result;\r\n                    const validateResult = await validate(fieldValue);\r\n                    const validateError = getValidateError(validateResult, validateRef, key);\r\n                    if (validateError) {\r\n                        result = Object.assign(Object.assign({}, validateError), appendErrorsCurry(key, validateError.message));\r\n                        if (validateAllFieldCriteria) {\r\n                            error[name] = result;\r\n                        }\r\n                    }\r\n                    else {\r\n                        result = previous;\r\n                    }\r\n                    return validateFunctions.length - 1 === index\r\n                        ? resolve(result)\r\n                        : result;\r\n                }, {});\r\n            });\r\n            if (!isEmptyObject(validationResult)) {\r\n                error[name] = Object.assign({ ref: validateRef }, validationResult);\r\n                if (!validateAllFieldCriteria) {\r\n                    return error;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    return error;\r\n};\n\nconst parseErrorSchema = (error, validateAllFieldCriteria) => isArray(error.inner)\r\n    ? error.inner.reduce((previous, { path, message, type }) => (Object.assign(Object.assign({}, previous), (previous[path] && validateAllFieldCriteria\r\n        ? {\r\n            [path]: appendErrors(path, validateAllFieldCriteria, previous, type, message),\r\n        }\r\n        : {\r\n            [path]: previous[path] || Object.assign({ message,\r\n                type }, (validateAllFieldCriteria\r\n                ? {\r\n                    types: { [type]: message || true },\r\n                }\r\n                : {})),\r\n        }))), {})\r\n    : {\r\n        [error.path]: { message: error.message, type: error.type },\r\n    };\r\nasync function validateWithSchema(validationSchema, validateAllFieldCriteria, data) {\r\n    try {\r\n        return {\r\n            values: await validationSchema.validate(data, { abortEarly: false }),\r\n            errors: {},\r\n        };\r\n    }\r\n    catch (e) {\r\n        return {\r\n            values: {},\r\n            errors: transformToNestObject(parseErrorSchema(e, validateAllFieldCriteria)),\r\n        };\r\n    }\r\n}\n\nvar getDefaultValue = (defaultValues, name, defaultValue) => isUndefined(defaultValues[name])\r\n    ? get(defaultValues, name, defaultValue)\r\n    : defaultValues[name];\n\nfunction flatArray(list) {\r\n    return list.reduce((a, b) => a.concat(isArray(b) ? flatArray(b) : b), []);\r\n}\n\nvar isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);\n\nconst getPath = (path, values) => {\r\n    const getInnerPath = (value, key, isObject) => {\r\n        const pathWithIndex = isObject ? `${path}.${key}` : `${path}[${key}]`;\r\n        return isPrimitive(value) ? pathWithIndex : getPath(pathWithIndex, value);\r\n    };\r\n    return isArray(values)\r\n        ? values.map((value, key) => getInnerPath(value, key))\r\n        : Object.entries(values).map(([key, value]) => getInnerPath(value, key, true));\r\n};\r\nvar getPath$1 = (parentPath, value) => flatArray(getPath(parentPath, value));\n\nvar assignWatchFields = (fieldValues, fieldName, watchFields, combinedDefaultValues) => {\r\n    let value;\r\n    if (isEmptyObject(fieldValues)) {\r\n        value = undefined;\r\n    }\r\n    else if (!isUndefined(fieldValues[fieldName])) {\r\n        watchFields.add(fieldName);\r\n        value = fieldValues[fieldName];\r\n    }\r\n    else {\r\n        value = get(transformToNestObject(fieldValues), fieldName);\r\n        if (!isUndefined(value)) {\r\n            getPath$1(fieldName, value).forEach(name => watchFields.add(name));\r\n        }\r\n    }\r\n    return isUndefined(value)\r\n        ? isObject(combinedDefaultValues)\r\n            ? getDefaultValue(combinedDefaultValues, fieldName)\r\n            : combinedDefaultValues\r\n        : value;\r\n};\n\nvar skipValidation = ({ hasError, isBlurEvent, isOnSubmit, isReValidateOnSubmit, isOnBlur, isReValidateOnBlur, isSubmitted, }) => (isOnSubmit && isReValidateOnSubmit) ||\r\n    (isOnSubmit && !isSubmitted) ||\r\n    (isOnBlur && !isBlurEvent && !hasError) ||\r\n    (isReValidateOnBlur && !isBlurEvent && hasError) ||\r\n    (isReValidateOnSubmit && isSubmitted);\n\nfunction getIsFieldsDifferent(referenceArray, differenceArray) {\r\n    let isMatch = false;\r\n    if (!isArray(referenceArray) ||\r\n        !isArray(differenceArray) ||\r\n        referenceArray.length !== differenceArray.length) {\r\n        return true;\r\n    }\r\n    for (let i = 0; i < referenceArray.length; i++) {\r\n        if (isMatch) {\r\n            break;\r\n        }\r\n        const dataA = referenceArray[i];\r\n        const dataB = differenceArray[i];\r\n        if (!dataB || Object.keys(dataA).length !== Object.keys(dataB).length) {\r\n            isMatch = true;\r\n            break;\r\n        }\r\n        for (const key in dataA) {\r\n            if (!dataB[key] || dataA[key] !== dataB[key]) {\r\n                isMatch = true;\r\n                break;\r\n            }\r\n        }\r\n    }\r\n    return isMatch;\r\n}\n\nconst isMatchFieldArrayName = (name, searchName) => name.startsWith(`${searchName}[`);\r\nvar isNameInFieldArray = (names, name) => [...names].reduce((prev, current) => (isMatchFieldArrayName(name, current) ? true : prev), false);\n\nfunction onDomRemove(element, onDetachCallback) {\r\n    const observer = new MutationObserver(() => {\r\n        if (isDetached(element)) {\r\n            observer.disconnect();\r\n            onDetachCallback();\r\n        }\r\n    });\r\n    observer.observe(window.document, {\r\n        childList: true,\r\n        subtree: true,\r\n    });\r\n    return observer;\r\n}\n\nconst unsetObject = (target) => {\r\n    for (const key in target) {\r\n        const data = target[key];\r\n        const isArrayObject = isArray(data);\r\n        if ((isObject(data) || isArrayObject) && !data.ref) {\r\n            unsetObject(data);\r\n        }\r\n        if (isUndefined(data) ||\r\n            isEmptyObject(data) ||\r\n            (isArrayObject && !target[key].filter(Boolean).length)) {\r\n            delete target[key];\r\n        }\r\n    }\r\n    return target;\r\n};\r\nconst unset = (target, paths) => {\r\n    paths.forEach(path => {\r\n        set(target, path, undefined);\r\n    });\r\n    return unsetObject(target);\r\n};\n\nvar modeChecker = (mode) => ({\r\n    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\r\n    isOnBlur: mode === VALIDATION_MODE.onBlur,\r\n    isOnChange: mode === VALIDATION_MODE.onChange,\r\n});\n\nconst { useRef, useState, useCallback, useEffect } = external_root_React_commonjs2_react_commonjs_react_amd_react_;\r\nfunction useForm({ mode = VALIDATION_MODE.onSubmit, reValidateMode = VALIDATION_MODE.onChange, validationSchema, defaultValues = {}, submitFocusError = true, validateCriteriaMode, } = {}) {\r\n    const fieldsRef = useRef({});\r\n    const validateAllFieldCriteria = validateCriteriaMode === 'all';\r\n    const errorsRef = useRef({});\r\n    const touchedFieldsRef = useRef({});\r\n    const watchFieldsRef = useRef(new Set());\r\n    const dirtyFieldsRef = useRef(new Set());\r\n    const fieldsWithValidationRef = useRef(new Set());\r\n    const validFieldsRef = useRef(new Set());\r\n    const isValidRef = useRef(true);\r\n    const defaultRenderValuesRef = useRef({});\r\n    const defaultValuesRef = useRef(defaultValues);\r\n    const isUnMount = useRef(false);\r\n    const isWatchAllRef = useRef(false);\r\n    const isSubmittedRef = useRef(false);\r\n    const isDirtyRef = useRef(false);\r\n    const submitCountRef = useRef(0);\r\n    const isSubmittingRef = useRef(false);\r\n    const handleChangeRef = useRef();\r\n    const resetFieldArrayFunctionRef = useRef({});\r\n    const fieldArrayNamesRef = useRef(new Set());\r\n    const [, render] = useState();\r\n    const { isOnBlur, isOnSubmit } = useRef(modeChecker(mode)).current;\r\n    const isWindowUndefined = typeof window === UNDEFINED;\r\n    const isWeb = typeof document !== UNDEFINED &&\r\n        !isWindowUndefined &&\r\n        !isUndefined(window.HTMLElement);\r\n    const isProxyEnabled = isWeb && 'Proxy' in window;\r\n    const readFormStateRef = useRef({\r\n        dirty: !isProxyEnabled,\r\n        isSubmitted: isOnSubmit,\r\n        submitCount: !isProxyEnabled,\r\n        touched: !isProxyEnabled,\r\n        isSubmitting: !isProxyEnabled,\r\n        isValid: !isProxyEnabled,\r\n    });\r\n    const { isOnBlur: isReValidateOnBlur, isOnSubmit: isReValidateOnSubmit, } = useRef(modeChecker(reValidateMode)).current;\r\n    defaultValuesRef.current = defaultValuesRef.current\r\n        ? defaultValuesRef.current\r\n        : defaultValues;\r\n    const reRender = useCallback(() => {\r\n        if (!isUnMount.current) {\r\n            render({});\r\n        }\r\n    }, []);\r\n    const validateFieldCurry = useCallback(validateField.bind(null, fieldsRef, validateAllFieldCriteria), []);\r\n    const validateFieldsSchemaCurry = useCallback(validateWithSchema.bind(null, validationSchema, validateAllFieldCriteria), [validationSchema]);\r\n    const renderBaseOnError = useCallback((name, error, shouldRender, skipReRender) => {\r\n        let shouldReRender = shouldRender ||\r\n            shouldUpdateWithError({\r\n                errors: errorsRef.current,\r\n                error,\r\n                name,\r\n                validFields: validFieldsRef.current,\r\n                fieldsWithValidation: fieldsWithValidationRef.current,\r\n            });\r\n        if (isEmptyObject(error)) {\r\n            if (fieldsWithValidationRef.current.has(name) || validationSchema) {\r\n                validFieldsRef.current.add(name);\r\n                shouldReRender = shouldReRender || get(errorsRef.current, name);\r\n            }\r\n            errorsRef.current = unset(errorsRef.current, [name]);\r\n        }\r\n        else {\r\n            validFieldsRef.current.delete(name);\r\n            shouldReRender = shouldReRender || !get(errorsRef.current, name);\r\n            set(errorsRef.current, name, error[name]);\r\n        }\r\n        if (shouldReRender && !skipReRender) {\r\n            reRender();\r\n            return true;\r\n        }\r\n    }, [reRender, validationSchema]);\r\n    const setFieldValue = useCallback((name, rawValue) => {\r\n        const field = fieldsRef.current[name];\r\n        if (!field) {\r\n            return false;\r\n        }\r\n        const ref = field.ref;\r\n        const { type } = ref;\r\n        const options = field.options;\r\n        const value = isWeb &&\r\n            ref instanceof window.HTMLElement &&\r\n            isNullOrUndefined(rawValue)\r\n            ? ''\r\n            : rawValue;\r\n        if (isRadioInput(type) && options) {\r\n            options.forEach(({ ref: radioRef }) => (radioRef.checked = radioRef.value === value));\r\n        }\r\n        else if (isFileInput(type)) {\r\n            if (value instanceof FileList || value === '') {\r\n                ref.files = value;\r\n            }\r\n            else {\r\n                ref.value = value;\r\n            }\r\n        }\r\n        else if (isMultipleSelect(type)) {\r\n            [...ref.options].forEach(selectRef => (selectRef.selected = value.includes(selectRef.value)));\r\n        }\r\n        else if (isCheckBoxInput(type) && options) {\r\n            options.length > 1\r\n                ? options.forEach(({ ref: checkboxRef }) => (checkboxRef.checked = value.includes(checkboxRef.value)))\r\n                : (options[0].ref.checked = !!value);\r\n        }\r\n        else {\r\n            ref.value = value;\r\n        }\r\n        return type;\r\n    }, [isWeb]);\r\n    const setDirty = (name) => {\r\n        if (!fieldsRef.current[name] || !readFormStateRef.current.dirty) {\r\n            return false;\r\n        }\r\n        const isFieldArray = isNameInFieldArray(fieldArrayNamesRef.current, name);\r\n        let isDirty = defaultRenderValuesRef.current[name] !==\r\n            getFieldValue(fieldsRef.current, fieldsRef.current[name].ref);\r\n        if (isFieldArray) {\r\n            console.log(defaultValuesRef.current);\r\n            const fieldArrayName = name.substring(0, name.indexOf('['));\r\n            isDirty = getIsFieldsDifferent(transformToNestObject(getFieldsValues(fieldsRef.current))[fieldArrayName], get(defaultValuesRef.current, fieldArrayName));\r\n        }\r\n        const isDirtyChanged = isFieldArray\r\n            ? isDirtyRef.current !== isDirty\r\n            : dirtyFieldsRef.current.has(name) !== isDirty;\r\n        if (isDirty) {\r\n            dirtyFieldsRef.current.add(name);\r\n        }\r\n        else {\r\n            dirtyFieldsRef.current.delete(name);\r\n        }\r\n        isDirtyRef.current = isFieldArray ? isDirty : !!dirtyFieldsRef.current.size;\r\n        return isDirtyChanged;\r\n    };\r\n    const setInternalValue = useCallback((name, value) => {\r\n        setFieldValue(name, value);\r\n        if (setDirty(name) ||\r\n            (!get(touchedFieldsRef.current, name) &&\r\n                readFormStateRef.current.touched)) {\r\n            return !!set(touchedFieldsRef.current, name, true);\r\n        }\r\n    }, [setFieldValue]);\r\n    const executeValidation = useCallback(async (name, shouldRender, skipReRender) => {\r\n        const field = fieldsRef.current[name];\r\n        if (!field) {\r\n            return false;\r\n        }\r\n        if (shouldRender) {\r\n            reRender();\r\n        }\r\n        const error = await validateField(fieldsRef, validateAllFieldCriteria, field);\r\n        renderBaseOnError(name, error, false, skipReRender);\r\n        return isEmptyObject(error);\r\n    }, [reRender, renderBaseOnError, validateAllFieldCriteria]);\r\n    const executeSchemaValidation = useCallback(async (payload, shouldRender) => {\r\n        const { errors } = await validateWithSchema(validationSchema, validateAllFieldCriteria, transformToNestObject(getFieldsValues(fieldsRef.current)));\r\n        const previousFormIsValid = isValidRef.current;\r\n        isValidRef.current = isEmptyObject(errors);\r\n        if (isArray(payload)) {\r\n            payload.forEach(name => {\r\n                if (errors[name]) {\r\n                    set(errorsRef.current, name, errors[name]);\r\n                }\r\n                else {\r\n                    unset(errorsRef.current, [name]);\r\n                }\r\n            });\r\n            reRender();\r\n        }\r\n        else {\r\n            const fieldName = payload;\r\n            const error = (get(errors, fieldName)\r\n                ? { [fieldName]: get(errors, fieldName) }\r\n                : {});\r\n            renderBaseOnError(fieldName, error, shouldRender || previousFormIsValid !== isValidRef.current);\r\n        }\r\n        return isEmptyObject(errorsRef.current);\r\n    }, [reRender, renderBaseOnError, validateAllFieldCriteria, validationSchema]);\r\n    const triggerValidation = useCallback(async (payload, shouldRender) => {\r\n        const fields = payload || Object.keys(fieldsRef.current);\r\n        if (validationSchema) {\r\n            return executeSchemaValidation(fields, shouldRender);\r\n        }\r\n        if (isArray(fields)) {\r\n            const result = await Promise.all(fields.map(async (data) => await executeValidation(data, false, true)));\r\n            reRender();\r\n            return result.every(Boolean);\r\n        }\r\n        return await executeValidation(fields, shouldRender);\r\n    }, [executeSchemaValidation, executeValidation, reRender, validationSchema]);\r\n    const setValue = useCallback((name, value, shouldValidate) => {\r\n        const shouldRender = setInternalValue(name, value) ||\r\n            isWatchAllRef.current ||\r\n            watchFieldsRef.current.has(name);\r\n        if (shouldValidate) {\r\n            return triggerValidation(name, shouldRender);\r\n        }\r\n        if (shouldRender) {\r\n            reRender();\r\n        }\r\n        return;\r\n    }, [reRender, setInternalValue, triggerValidation]);\r\n    handleChangeRef.current = handleChangeRef.current\r\n        ? handleChangeRef.current\r\n        : async ({ type, target }) => {\r\n            const name = target ? target.name : '';\r\n            const fields = fieldsRef.current;\r\n            const errors = errorsRef.current;\r\n            const field = fields[name];\r\n            const currentError = get(errors, name);\r\n            let error;\r\n            if (!field) {\r\n                return;\r\n            }\r\n            const isBlurEvent = type === EVENTS.BLUR;\r\n            const shouldSkipValidation = skipValidation({\r\n                hasError: !!currentError,\r\n                isBlurEvent,\r\n                isOnSubmit,\r\n                isReValidateOnSubmit,\r\n                isOnBlur,\r\n                isReValidateOnBlur,\r\n                isSubmitted: isSubmittedRef.current,\r\n            });\r\n            const shouldUpdateDirty = setDirty(name);\r\n            let shouldUpdateState = isWatchAllRef.current ||\r\n                watchFieldsRef.current.has(name) ||\r\n                shouldUpdateDirty;\r\n            if (isBlurEvent &&\r\n                !get(touchedFieldsRef.current, name) &&\r\n                readFormStateRef.current.touched) {\r\n                set(touchedFieldsRef.current, name, true);\r\n                shouldUpdateState = true;\r\n            }\r\n            if (shouldSkipValidation) {\r\n                return shouldUpdateState && reRender();\r\n            }\r\n            if (validationSchema) {\r\n                const { errors } = await validateWithSchema(validationSchema, validateAllFieldCriteria, transformToNestObject(getFieldsValues(fields)));\r\n                const validForm = isEmptyObject(errors);\r\n                error = (get(errors, name)\r\n                    ? { [name]: get(errors, name) }\r\n                    : {});\r\n                if (isValidRef.current !== validForm) {\r\n                    shouldUpdateState = true;\r\n                }\r\n                isValidRef.current = validForm;\r\n            }\r\n            else {\r\n                error = await validateField(fieldsRef, validateAllFieldCriteria, field);\r\n            }\r\n            if (!renderBaseOnError(name, error) && shouldUpdateState) {\r\n                reRender();\r\n            }\r\n        };\r\n    const validateSchemaIsValid = useCallback(() => {\r\n        const fieldValues = isEmptyObject(defaultValuesRef.current)\r\n            ? getFieldsValues(fieldsRef.current)\r\n            : defaultValuesRef.current;\r\n        validateFieldsSchemaCurry(transformToNestObject(fieldValues)).then(({ errors }) => {\r\n            const previousFormIsValid = isValidRef.current;\r\n            isValidRef.current = isEmptyObject(errors);\r\n            if (previousFormIsValid && previousFormIsValid !== isValidRef.current) {\r\n                reRender();\r\n            }\r\n        });\r\n    }, [reRender, validateFieldsSchemaCurry]);\r\n    const resetFieldRef = useCallback((name) => {\r\n        errorsRef.current = unset(errorsRef.current, [name]);\r\n        touchedFieldsRef.current = unset(touchedFieldsRef.current, [name]);\r\n        defaultRenderValuesRef.current = unset(defaultRenderValuesRef.current, [\r\n            name,\r\n        ]);\r\n        [\r\n            dirtyFieldsRef,\r\n            fieldsWithValidationRef,\r\n            validFieldsRef,\r\n            watchFieldsRef,\r\n        ].forEach(data => data.current.delete(name));\r\n        if (readFormStateRef.current.isValid ||\r\n            readFormStateRef.current.touched) {\r\n            reRender();\r\n        }\r\n        if (validationSchema) {\r\n            validateSchemaIsValid();\r\n        }\r\n    }, [reRender]);\r\n    const removeEventListenerAndRef = useCallback((field, forceDelete) => {\r\n        if (!field) {\r\n            return;\r\n        }\r\n        if (!isUndefined(handleChangeRef.current)) {\r\n            findRemovedFieldAndRemoveListener(fieldsRef.current, handleChangeRef.current, field, forceDelete);\r\n        }\r\n        resetFieldRef(field.ref.name);\r\n    }, [resetFieldRef]);\r\n    function clearError(name) {\r\n        if (isUndefined(name)) {\r\n            errorsRef.current = {};\r\n        }\r\n        else {\r\n            unset(errorsRef.current, isArray(name) ? name : [name]);\r\n        }\r\n        reRender();\r\n    }\r\n    const setInternalError = ({ name, type, types, message, preventRender, }) => {\r\n        const field = fieldsRef.current[name];\r\n        if (!isSameError(errorsRef.current[name], type, message)) {\r\n            set(errorsRef.current, name, {\r\n                type,\r\n                types,\r\n                message,\r\n                ref: field ? field.ref : {},\r\n                isManual: true,\r\n            });\r\n            if (!preventRender) {\r\n                reRender();\r\n            }\r\n        }\r\n    };\r\n    function setError(name, type = '', message) {\r\n        if (isString(name)) {\r\n            setInternalError(Object.assign({ name }, (isObject(type)\r\n                ? {\r\n                    types: type,\r\n                    type: '',\r\n                }\r\n                : {\r\n                    type,\r\n                    message,\r\n                })));\r\n        }\r\n        else if (isArray(name)) {\r\n            name.forEach(error => setInternalError(Object.assign(Object.assign({}, error), { preventRender: true })));\r\n            reRender();\r\n        }\r\n    }\r\n    function watch(fieldNames, defaultValue) {\r\n        const combinedDefaultValues = isUndefined(defaultValue)\r\n            ? isUndefined(defaultValuesRef.current)\r\n                ? {}\r\n                : defaultValuesRef.current\r\n            : defaultValue;\r\n        const fieldValues = getFieldsValues(fieldsRef.current);\r\n        const watchFields = watchFieldsRef.current;\r\n        if (isProxyEnabled) {\r\n            readFormStateRef.current.dirty = true;\r\n        }\r\n        if (isString(fieldNames)) {\r\n            return assignWatchFields(fieldValues, fieldNames, watchFields, combinedDefaultValues);\r\n        }\r\n        if (isArray(fieldNames)) {\r\n            return fieldNames.reduce((previous, name) => {\r\n                let value;\r\n                if (isEmptyObject(fieldsRef.current) &&\r\n                    isObject(combinedDefaultValues)) {\r\n                    value = getDefaultValue(combinedDefaultValues, name);\r\n                }\r\n                else {\r\n                    value = assignWatchFields(fieldValues, name, watchFields, combinedDefaultValues);\r\n                }\r\n                return Object.assign(Object.assign({}, previous), { [name]: value });\r\n            }, {});\r\n        }\r\n        isWatchAllRef.current = true;\r\n        const result = (!isEmptyObject(fieldValues) && fieldValues) ||\r\n            defaultValue ||\r\n            defaultValuesRef.current;\r\n        return fieldNames && fieldNames.nest\r\n            ? transformToNestObject(result)\r\n            : result;\r\n    }\r\n    function unregister(names) {\r\n        if (!isEmptyObject(fieldsRef.current)) {\r\n            (isArray(names) ? names : [names]).forEach(fieldName => removeEventListenerAndRef(fieldsRef.current[fieldName], true));\r\n        }\r\n    }\r\n    function registerFieldsRef(ref, validateOptions = {}) {\r\n        if (!ref.name) {\r\n            return console.warn('Missing name @', ref);\r\n        }\r\n        const { name, type, value } = ref;\r\n        const fieldAttributes = Object.assign({ ref }, validateOptions);\r\n        const fields = fieldsRef.current;\r\n        const isRadioOrCheckbox = isRadioInput(type) || isCheckBoxInput(type);\r\n        let currentField = fields[name];\r\n        let isEmptyDefaultValue = true;\r\n        let isFieldArray = false;\r\n        let defaultValue;\r\n        if (isRadioOrCheckbox\r\n            ? currentField &&\r\n                isArray(currentField.options) &&\r\n                currentField.options.find(({ ref }) => value === ref.value)\r\n            : currentField) {\r\n            fields[name] = Object.assign(Object.assign({}, currentField), validateOptions);\r\n            return;\r\n        }\r\n        if (type) {\r\n            const mutationWatcher = onDomRemove(ref, () => removeEventListenerAndRef(fieldAttributes));\r\n            if (isRadioOrCheckbox) {\r\n                currentField = Object.assign({ options: [\r\n                        ...((currentField && currentField.options) || []),\r\n                        {\r\n                            ref,\r\n                            mutationWatcher,\r\n                        },\r\n                    ], ref: { type, name } }, validateOptions);\r\n            }\r\n            else {\r\n                currentField = Object.assign(Object.assign({}, fieldAttributes), { mutationWatcher });\r\n            }\r\n        }\r\n        else {\r\n            currentField = fieldAttributes;\r\n        }\r\n        fields[name] = currentField;\r\n        if (!isEmptyObject(defaultValuesRef.current)) {\r\n            defaultValue = getDefaultValue(defaultValuesRef.current, name);\r\n            isEmptyDefaultValue = isUndefined(defaultValue);\r\n            isFieldArray = isNameInFieldArray(fieldArrayNamesRef.current, name);\r\n            if (!isEmptyDefaultValue && !isFieldArray) {\r\n                setFieldValue(name, defaultValue);\r\n            }\r\n        }\r\n        if (validationSchema && readFormStateRef.current.isValid) {\r\n            validateSchemaIsValid();\r\n        }\r\n        else if (!isEmptyObject(validateOptions)) {\r\n            fieldsWithValidationRef.current.add(name);\r\n            if (!isOnSubmit && readFormStateRef.current.isValid) {\r\n                validateFieldCurry(currentField).then(error => {\r\n                    const previousFormIsValid = isValidRef.current;\r\n                    if (isEmptyObject(error)) {\r\n                        validFieldsRef.current.add(name);\r\n                    }\r\n                    else {\r\n                        isValidRef.current = false;\r\n                    }\r\n                    if (previousFormIsValid !== isValidRef.current) {\r\n                        reRender();\r\n                    }\r\n                });\r\n            }\r\n        }\r\n        if (!defaultRenderValuesRef.current[name] &&\r\n            !(isFieldArray && isEmptyDefaultValue)) {\r\n            defaultRenderValuesRef.current[name] = isEmptyDefaultValue\r\n                ? getFieldValue(fields, currentField.ref)\r\n                : defaultValue;\r\n        }\r\n        if (!type) {\r\n            return;\r\n        }\r\n        const fieldToAttachListener = isRadioOrCheckbox && currentField.options\r\n            ? currentField.options[currentField.options.length - 1]\r\n            : currentField;\r\n        attachEventListeners({\r\n            field: fieldToAttachListener,\r\n            isRadioOrCheckbox,\r\n            handleChange: handleChangeRef.current,\r\n        });\r\n    }\r\n    function register(refOrValidationOptions, validationOptions) {\r\n        if (isWindowUndefined || !refOrValidationOptions) {\r\n            return;\r\n        }\r\n        if (isString(refOrValidationOptions)) {\r\n            registerFieldsRef({ name: refOrValidationOptions }, validationOptions);\r\n            return;\r\n        }\r\n        if (isObject(refOrValidationOptions) && 'name' in refOrValidationOptions) {\r\n            registerFieldsRef(refOrValidationOptions, validationOptions);\r\n            return;\r\n        }\r\n        return (ref) => ref && registerFieldsRef(ref, refOrValidationOptions);\r\n    }\r\n    const handleSubmit = useCallback((callback) => async (e) => {\r\n        if (e) {\r\n            e.preventDefault();\r\n            e.persist();\r\n        }\r\n        let fieldErrors;\r\n        let fieldValues;\r\n        const fields = fieldsRef.current;\r\n        if (readFormStateRef.current.isSubmitting) {\r\n            isSubmittingRef.current = true;\r\n            reRender();\r\n        }\r\n        try {\r\n            if (validationSchema) {\r\n                fieldValues = getFieldsValues(fields);\r\n                const { errors, values } = await validateFieldsSchemaCurry(transformToNestObject(fieldValues));\r\n                errorsRef.current = errors;\r\n                fieldErrors = errors;\r\n                fieldValues = values;\r\n            }\r\n            else {\r\n                const { errors, values, } = await Object.values(fields).reduce(async (previous, field) => {\r\n                    if (!field) {\r\n                        return previous;\r\n                    }\r\n                    const resolvedPrevious = await previous;\r\n                    const { ref, ref: { name }, } = field;\r\n                    if (!fields[name]) {\r\n                        return Promise.resolve(resolvedPrevious);\r\n                    }\r\n                    const fieldError = await validateFieldCurry(field);\r\n                    if (fieldError[name]) {\r\n                        set(resolvedPrevious.errors, name, fieldError[name]);\r\n                        validFieldsRef.current.delete(name);\r\n                        return Promise.resolve(resolvedPrevious);\r\n                    }\r\n                    if (fieldsWithValidationRef.current.has(name)) {\r\n                        validFieldsRef.current.add(name);\r\n                    }\r\n                    resolvedPrevious.values[name] = getFieldValue(fields, ref);\r\n                    return Promise.resolve(resolvedPrevious);\r\n                }, Promise.resolve({\r\n                    errors: {},\r\n                    values: {},\r\n                }));\r\n                fieldErrors = errors;\r\n                fieldValues = values;\r\n            }\r\n            if (isEmptyObject(fieldErrors)) {\r\n                errorsRef.current = {};\r\n                await callback(transformToNestObject(fieldValues), e);\r\n            }\r\n            else {\r\n                if (submitFocusError) {\r\n                    for (const key in fieldsRef.current) {\r\n                        if (get(fieldErrors, key)) {\r\n                            const field = fieldsRef.current[key];\r\n                            if (field) {\r\n                                if (field.ref.focus) {\r\n                                    field.ref.focus();\r\n                                    break;\r\n                                }\r\n                                else if (field.options) {\r\n                                    field.options[0].ref.focus();\r\n                                    break;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                errorsRef.current = fieldErrors;\r\n            }\r\n        }\r\n        finally {\r\n            isSubmittedRef.current = true;\r\n            isSubmittingRef.current = false;\r\n            submitCountRef.current = submitCountRef.current + 1;\r\n            reRender();\r\n        }\r\n    }, [\r\n        reRender,\r\n        submitFocusError,\r\n        validateFieldCurry,\r\n        validateFieldsSchemaCurry,\r\n        validationSchema,\r\n    ]);\r\n    const resetRefs = () => {\r\n        errorsRef.current = {};\r\n        fieldsRef.current = {};\r\n        touchedFieldsRef.current = {};\r\n        validFieldsRef.current = new Set();\r\n        fieldsWithValidationRef.current = new Set();\r\n        defaultRenderValuesRef.current = {};\r\n        watchFieldsRef.current = new Set();\r\n        dirtyFieldsRef.current = new Set();\r\n        isWatchAllRef.current = false;\r\n        isSubmittedRef.current = false;\r\n        isDirtyRef.current = false;\r\n        isValidRef.current = true;\r\n        submitCountRef.current = 0;\r\n    };\r\n    const reset = (values) => {\r\n        for (const value of Object.values(fieldsRef.current)) {\r\n            if (value && value.ref && value.ref.closest) {\r\n                try {\r\n                    value.ref.closest('form').reset();\r\n                    break;\r\n                }\r\n                catch (_a) { }\r\n            }\r\n        }\r\n        if (values) {\r\n            defaultValuesRef.current = values;\r\n        }\r\n        Object.values(resetFieldArrayFunctionRef.current).forEach(resetFieldArray => isFunction(resetFieldArray) && resetFieldArray(values));\r\n        resetRefs();\r\n        reRender();\r\n    };\r\n    const getValues = (payload) => {\r\n        const fieldValues = getFieldsValues(fieldsRef.current);\r\n        const outputValues = isEmptyObject(fieldValues)\r\n            ? defaultValuesRef.current\r\n            : fieldValues;\r\n        return payload && payload.nest\r\n            ? transformToNestObject(outputValues)\r\n            : outputValues;\r\n    };\r\n    useEffect(() => () => {\r\n        isUnMount.current = true;\r\n        fieldsRef.current &&\r\n            Object.values(fieldsRef.current).forEach((field) => removeEventListenerAndRef(field, true));\r\n    }, [removeEventListenerAndRef]);\r\n    if (!validationSchema) {\r\n        isValidRef.current =\r\n            validFieldsRef.current.size >= fieldsWithValidationRef.current.size &&\r\n                isEmptyObject(errorsRef.current);\r\n    }\r\n    const formState = {\r\n        dirty: isDirtyRef.current,\r\n        isSubmitted: isSubmittedRef.current,\r\n        submitCount: submitCountRef.current,\r\n        touched: touchedFieldsRef.current,\r\n        isSubmitting: isSubmittingRef.current,\r\n        isValid: isOnSubmit\r\n            ? isSubmittedRef.current && isEmptyObject(errorsRef.current)\r\n            : isEmptyObject(fieldsRef.current) || isValidRef.current,\r\n    };\r\n    const control = {\r\n        register,\r\n        unregister,\r\n        setValue,\r\n        triggerValidation,\r\n        formState,\r\n        mode: {\r\n            isOnBlur,\r\n            isOnSubmit,\r\n        },\r\n        reValidateMode: {\r\n            isReValidateOnBlur,\r\n            isReValidateOnSubmit,\r\n        },\r\n        errors: errorsRef.current,\r\n        fieldsRef,\r\n        resetFieldArrayFunctionRef,\r\n        fieldArrayNamesRef,\r\n        isDirtyRef,\r\n        readFormStateRef,\r\n        defaultValuesRef,\r\n    };\r\n    return {\r\n        watch,\r\n        control,\r\n        handleSubmit,\r\n        setValue,\r\n        triggerValidation,\r\n        getValues: useCallback(getValues, []),\r\n        reset: useCallback(reset, [reRender]),\r\n        register: useCallback(register, [defaultValuesRef.current]),\r\n        unregister: useCallback(unregister, [removeEventListenerAndRef]),\r\n        clearError: useCallback(clearError, []),\r\n        setError: useCallback(setError, []),\r\n        errors: errorsRef.current,\r\n        formState: isProxyEnabled\r\n            ? new Proxy(formState, {\r\n                get: (obj, prop) => {\r\n                    if (prop in obj) {\r\n                        readFormStateRef.current[prop] = true;\r\n                        return obj[prop];\r\n                    }\r\n                    return {};\r\n                },\r\n            })\r\n            : formState,\r\n    };\r\n}\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n\r\nfunction react_hook_form_es_rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\n\nconst FormGlobalContext = (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.createContext)(null);\r\nfunction useFormContext() {\r\n    return (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useContext)(FormGlobalContext);\r\n}\r\nfunction FormContext(_a) {\r\n    var { children, formState, errors } = _a, restMethods = react_hook_form_es_rest(_a, [\"children\", \"formState\", \"errors\"]);\r\n    return ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement)(FormGlobalContext.Provider, { value: Object.assign(Object.assign({}, restMethods), { formState, errors }) }, children));\r\n}\n\nvar generateId = () => {\r\n    const d = typeof performance === UNDEFINED ? Date.now() : performance.now() * 1000;\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\r\n        const r = (Math.random() * 16 + d) % 16 | 0;\r\n        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\r\n    });\r\n};\n\nconst appendId = (value) => (Object.assign(Object.assign({}, value), { id: generateId() }));\r\nconst mapIds = (data) => (isArray(data) ? data : []).map(value => appendId(value));\n\nvar removeArrayAt = (data, index) => !isUndefined(index) && isArray(data)\r\n    ? [...data.slice(0, index), ...data.slice(index + 1)]\r\n    : [];\n\nvar moveArrayAt = (data, from, to) => isArray(data) && data.splice(to, 0, data.splice(from, 1)[0]);\n\nvar swapArrayAt = (fields, indexA, indexB) => isArray(fields) &&\r\n    ([fields[indexA], fields[indexB]] = [fields[indexB], fields[indexA]]);\n\nfunction useFieldArray({ control, name }) {\r\n    const methods = useFormContext();\r\n    const { resetFieldArrayFunctionRef, fieldArrayNamesRef, fieldsRef, defaultValuesRef, unregister, isDirtyRef, readFormStateRef, } = control || methods.control;\r\n    const memoizedDefaultValues = (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useRef)(get(defaultValuesRef.current, name, []));\r\n    const [fields, setField] = (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useState)(mapIds(memoizedDefaultValues.current));\r\n    const getFieldValuesByName = (fields, name) => transformToNestObject(getFieldsValues(fields))[name];\r\n    const resetFields = (flagOrFields) => {\r\n        if (readFormStateRef.current.dirty) {\r\n            isDirtyRef.current = isUndefined(flagOrFields)\r\n                ? true\r\n                : getIsFieldsDifferent(flagOrFields, memoizedDefaultValues.current);\r\n        }\r\n        for (const key in fieldsRef.current) {\r\n            if (isMatchFieldArrayName(key, name)) {\r\n                unregister(key);\r\n            }\r\n        }\r\n    };\r\n    const append = (value) => {\r\n        if (readFormStateRef.current.dirty) {\r\n            isDirtyRef.current = true;\r\n        }\r\n        setField([...fields, appendId(value)]);\r\n    };\r\n    const prepend = (value) => {\r\n        resetFields();\r\n        setField(mapIds([appendId(value), ...fields]));\r\n    };\r\n    const remove = (index) => {\r\n        const updatedFields = removeArrayAt(getFieldValuesByName(fieldsRef.current, name), index);\r\n        resetFields(updatedFields);\r\n        setField(mapIds(updatedFields));\r\n    };\r\n    const insert = (index, value) => {\r\n        const fieldValues = getFieldValuesByName(fieldsRef.current, name);\r\n        resetFields();\r\n        setField(mapIds([\r\n            ...fieldValues.slice(0, index),\r\n            appendId(value),\r\n            ...fieldValues.slice(index),\r\n        ]));\r\n    };\r\n    const swap = (indexA, indexB) => {\r\n        const fieldValues = getFieldValuesByName(fieldsRef.current, name);\r\n        swapArrayAt(fieldValues, indexA, indexB);\r\n        resetFields(fieldValues);\r\n        setField(mapIds(fieldValues));\r\n    };\r\n    const move = (from, to) => {\r\n        const fieldValues = getFieldValuesByName(fieldsRef.current, name);\r\n        moveArrayAt(fieldValues, from, to);\r\n        resetFields(fieldValues);\r\n        setField(mapIds(fieldValues));\r\n    };\r\n    const reset = (values) => {\r\n        resetFields();\r\n        setField(mapIds(get(values, name)));\r\n        memoizedDefaultValues.current = get(defaultValuesRef.current, name, []);\r\n    };\r\n    (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect)(() => {\r\n        const resetFunctions = resetFieldArrayFunctionRef.current;\r\n        const fieldArrayNames = fieldArrayNamesRef.current;\r\n        fieldArrayNames.add(name);\r\n        resetFunctions[name] = reset;\r\n        return () => {\r\n            resetFields();\r\n            delete resetFunctions[name];\r\n            fieldArrayNames.delete(name);\r\n        };\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [name]);\r\n    return {\r\n        swap,\r\n        move,\r\n        prepend,\r\n        append,\r\n        remove,\r\n        insert,\r\n        fields,\r\n    };\r\n}\n\nvar getInputValue = (target, isCheckbox) => {\r\n    if (isNullOrUndefined(target)) {\r\n        return target;\r\n    }\r\n    return isCheckbox\r\n        ? isUndefined(target.checked)\r\n            ? target\r\n            : target.checked\r\n        : isUndefined(target.value)\r\n            ? target\r\n            : target.value;\r\n};\n\nconst Controller = (_a) => {\r\n    var { name, rules, as: InnerComponent, onChange, onChangeName = VALIDATION_MODE.onChange, onBlurName = VALIDATION_MODE.onBlur, valueName, defaultValue, control } = _a, rest = react_hook_form_es_rest(_a, [\"name\", \"rules\", \"as\", \"onChange\", \"onChangeName\", \"onBlurName\", \"valueName\", \"defaultValue\", \"control\"]);\r\n    const methods = useFormContext();\r\n    const { defaultValuesRef, setValue, register, unregister, errors, triggerValidation, mode: { isOnSubmit, isOnBlur }, reValidateMode: { isReValidateOnBlur, isReValidateOnSubmit }, formState: { isSubmitted }, fieldsRef, fieldArrayNamesRef, } = control || methods.control;\r\n    const [value, setInputStateValue] = (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useState)(isUndefined(defaultValue)\r\n        ? get(defaultValuesRef.current, name)\r\n        : defaultValue);\r\n    const valueRef = (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useRef)(value);\r\n    const isCheckboxInput = isBoolean(value);\r\n    const shouldValidate = () => !skipValidation({\r\n        hasError: !!get(errors, name),\r\n        isOnBlur,\r\n        isOnSubmit,\r\n        isReValidateOnBlur,\r\n        isReValidateOnSubmit,\r\n        isSubmitted,\r\n    });\r\n    const commonTask = (target) => {\r\n        const data = getInputValue(target, isCheckboxInput);\r\n        setInputStateValue(data);\r\n        valueRef.current = data;\r\n        return data;\r\n    };\r\n    const eventWrapper = (event) => (...arg) => setValue(name, commonTask(event(arg)), shouldValidate());\r\n    const handleChange = (e) => {\r\n        const data = commonTask(e && e.target ? e.target : e);\r\n        setValue(name, data, shouldValidate());\r\n    };\r\n    const registerField = () => register(Object.defineProperty({\r\n        name,\r\n    }, VALUE, {\r\n        set(data) {\r\n            setInputStateValue(data);\r\n            valueRef.current = data;\r\n        },\r\n        get() {\r\n            return valueRef.current;\r\n        },\r\n    }), Object.assign({}, rules));\r\n    if (!fieldsRef.current[name]) {\r\n        registerField();\r\n    }\r\n    (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect)(() => {\r\n        const fieldArrayNames = fieldArrayNamesRef.current;\r\n        registerField();\r\n        return () => {\r\n            if (!isNameInFieldArray(fieldArrayNames, name)) {\r\n                unregister(name);\r\n            }\r\n        };\r\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    [name]);\r\n    const props = Object.assign(Object.assign(Object.assign(Object.assign({ name }, rest), (onChange\r\n        ? { [onChangeName]: eventWrapper(onChange) }\r\n        : { [onChangeName]: handleChange })), (isOnBlur || isReValidateOnBlur\r\n        ? { [onBlurName]: () => triggerValidation(name) }\r\n        : {})), { [valueName || (isCheckboxInput ? 'checked' : VALUE)]: value });\r\n    return (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.isValidElement)(InnerComponent) ? ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.cloneElement)(InnerComponent, props)) : ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement)(InnerComponent, Object.assign({}, props)));\r\n};\n\nconst ErrorMessage = ({ as: InnerComponent, errors, name, children, }) => {\r\n    const methods = useFormContext();\r\n    const { message, types } = get(errors || methods.errors, name, {});\r\n    if (!message) {\r\n        return null;\r\n    }\r\n    const props = {\r\n        children: children ? children({ message, messages: types }) : message,\r\n    };\r\n    return InnerComponent ? ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.isValidElement)(InnerComponent) ? ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.cloneElement)(InnerComponent, props)) : ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement)(InnerComponent, Object.assign({}, props)))) : ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement)(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, Object.assign({}, props)));\r\n};\n\n\n\n;// ../src/models/Enums.ts\nvar EContactMethod;\n(function (EContactMethod) {\n    EContactMethod[\"PHONE\"] = \"Phone\";\n    EContactMethod[\"TEXT_MESSAGE\"] = \"TextMessage\";\n    EContactMethod[\"EMAIL\"] = \"Email\";\n})(EContactMethod || (EContactMethod = {}));\nvar EDuration;\n(function (EDuration) {\n    EDuration[\"AM\"] = \"AM\";\n    EDuration[\"PM\"] = \"PM\";\n    EDuration[\"Evening\"] = \"Evening\";\n    EDuration[\"AllDay\"] = \"AllDay\";\n    EDuration[\"Item0810\"] = \"Item0810\";\n    EDuration[\"Item1012\"] = \"Item1012\";\n    EDuration[\"Item1315\"] = \"Item1315\";\n    EDuration[\"Item1517\"] = \"Item1517\";\n    EDuration[\"Item1719\"] = \"Item1719\";\n    EDuration[\"Item1921\"] = \"Item1921\";\n})(EDuration || (EDuration = {}));\nvar EPreferredContactMethod;\n(function (EPreferredContactMethod) {\n    EPreferredContactMethod[\"EMAIL\"] = \"Email\";\n    EPreferredContactMethod[\"TEXT_MESSAGE\"] = \"TextMessage\";\n    EPreferredContactMethod[\"PHONE\"] = \"Phone\";\n})(EPreferredContactMethod || (EPreferredContactMethod = {}));\n\n;// ../src/utils/AppointmentUtils.ts\n\n\nfunction stripTimeBit(date) {\n    try {\n        var fragments = date.split(\"T\");\n        var newDate = new Date(fragments[0]);\n        newDate.setMinutes(new Date(date).getMinutes() + new Date(date).getTimezoneOffset());\n        newDate.setHours(0);\n        newDate.setMinutes(0);\n        return newDate;\n    }\n    catch (e) {\n        return date;\n    }\n}\nfunction mapEnum(enumerable, fn) {\n    var enumMembers = Object.keys(enumerable).map(function (key) { return enumerable[key]; });\n    return enumMembers.map(function (m) { return fn(m); });\n}\nvar noSpecialCharRegex = /^[a-zA-Z0-9]+$/i;\nvar emailRegex = /^[a-z0-9`!#\\$%&\\*\\+\\/=\\?\\^\\'\\-_]+((\\.)+[a-z0-9`!#\\$%&\\*\\+\\/=\\?\\^\\'\\-_]+)*@([a-z0-9]+([\\-][a-z0-9])*)+([\\.]([a-z0-9]+([\\-][a-z0-9])*)+)+$/i;\nvar formattedPhoneRegex = /^[0-9]\\d{2}-\\d{3}-\\d{4}$/i;\nvar getMessagesList = function (errors) {\n    var errorslist = [];\n    var action;\n    if (external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getFlowType() === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.INTERNET)\n        action = 523;\n    if (external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getFlowType() === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE)\n        action = 508;\n    Object.keys(errors).map(function (k) {\n        var err = errors[k];\n        errorslist.push({\n            id: err.ref.name,\n            error: err.type\n        });\n    });\n    external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackError(errorslist.map(function (err) { return ({\n        code: err.id,\n        type: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.EErrorType.Validation,\n        layer: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.EApplicationLayer.Frontend,\n        description: err.error\n    }); }), action);\n    return errorslist;\n};\nfunction autoFormat(value) {\n    var newVal = filterNumbers(value);\n    newVal = newVal.substr(0, 10);\n    return newVal.length === 10 ? newVal.slice(0, 3) + \"-\" + newVal.slice(3, 6) + \"-\" + newVal.slice(6) : newVal;\n}\nfunction filterNumbers(value) {\n    var num = value.replace(/\\D/g, \"\");\n    return num;\n}\nfunction ValueOf(val) {\n    return val;\n}\nfunction getSelectedDate(dates) {\n    var selectedDates = dates.filter(function (date) { return date.timeSlots.find(function (time) { return time.isSelected === true; }); });\n    return selectedDates.length > 0 ? selectedDates : [dates[0]];\n}\nvar getPrimaryValue = function (method, value) {\n    var _a, _b;\n    switch (method) {\n        case EContactMethod.EMAIL:\n            return value === null || value === void 0 ? void 0 : value.email;\n        case EContactMethod.PHONE:\n            return ((_a = value === null || value === void 0 ? void 0 : value.primaryPhone) === null || _a === void 0 ? void 0 : _a.phoneNumber) && autoFormat((_b = value === null || value === void 0 ? void 0 : value.primaryPhone) === null || _b === void 0 ? void 0 : _b.phoneNumber);\n        case EContactMethod.TEXT_MESSAGE:\n            return (value === null || value === void 0 ? void 0 : value.textMessage) && autoFormat(value === null || value === void 0 ? void 0 : value.textMessage);\n        default:\n            return \"\";\n    }\n};\n\n// EXTERNAL MODULE: external {\"root\":\"ReactIntl\",\"commonjs2\":\"react-intl\",\"commonjs\":\"react-intl\",\"amd\":\"react-intl\"}\nvar external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_ = __webpack_require__(\"react-intl\");\n;// ../src/views/Componenets/FormElements/Checkbox.tsx\n\n\n\n\n\nvar Checkbox = function (props) {\n    var privateProps = __assign(__assign({}, defaultProps), props);\n    var label = privateProps.label, value = privateProps.value, handleChange = privateProps.handleChange, subLabel = privateProps.subLabel, checked = privateProps.checked;\n    var register = useFormContext().register;\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock flexCol-xs margin-15-bottom\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { htmlFor: \"additionalPhoneNumber\", className: \"installation-form-label\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtBold block\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: label })),\n            subLabel ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtItalic block txtNormal\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: subLabel })) : null),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol margin-5-top\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { className: \"graphical_ctrl graphical_ctrl_checkbox txtNormal\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: value + \"_LABEL\" }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"input\", { type: \"checkbox\", ref: register, id: label, name: label, defaultChecked: checked, onChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"ctrl_element chk_radius\" })))));\n};\nvar defaultProps = {\n    checked: false\n};\n\n;// ../src/views/Componenets/FormElements/RadioBtn.tsx\n\n\n\n\nvar RadioBtn = function (props) {\n    var privateProps = __assign(__assign({}, RadioBtn_defaultProps), props);\n    var label = privateProps.label, value = privateProps.value, handleChange = privateProps.handleChange, checked = privateProps.checked, requiredInput = privateProps.requiredInput;\n    var register = useFormContext().register;\n    return label ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { className: \"graphical_ctrl pointer ctrl_radioBtn margin-10-bottom\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { className: \"txtBold block\", htmlFor: \"option_\".concat(value) },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: value })),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"input\", { type: \"radio\", id: \"option_\".concat(value), ref: register({ required: requiredInput }), name: label, value: value, checked: checked, onChange: function (e) { return handleChange(e); } }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"ctrl_element\" }),\n        value === \"OTHER\" && checked === true ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"topArrow text-left otherOption\", \"aria-hidden\": \"true\" }) : null) : null;\n};\nvar RadioBtn_defaultProps = {\n    checked: false,\n    requiredInput: false\n};\n\n;// ../src/views/Componenets/FormElements/TextArea.tsx\n\n\n\n\n\nvar TextArea = function (props) {\n    var privateProps = __assign(__assign({}, TextArea_defaultProps), props);\n    var label = privateProps.label, required = privateProps.required, value = privateProps.value, subLabel = privateProps.subLabel, handleChange = privateProps.handleChange, requiredInput = privateProps.requiredInput, maxLength = privateProps.maxLength;\n    var register = useFormContext().register;\n    var _a = __read(external_root_React_commonjs2_react_commonjs_react_amd_react_.useState((maxLength || 0) - (value || \"\").length), 2), crCount = _a[0], setCount = _a[1];\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock flexCol-xs margin-15-bottom\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { htmlFor: label, className: \"installation-form-label\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtBold\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: label })),\n            required ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtNormal\" }, \"(optional)\") : \"\",\n            subLabel ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtItalic block txtNormal\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: subLabel })) : null),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"textarea\", { ref: register({ required: requiredInput }), id: label, name: label, defaultValue: value, maxLength: maxLength, className: \"brf3-textarea form-control\", onChange: function (e) {\n                    setCount((maxLength || 0) - (e.currentTarget.value || \"\").length);\n                    handleChange(e);\n                } }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", null,\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: label + \"_DESCRIPTION\", values: { max: maxLength, count: crCount } })))));\n};\nvar TextArea_defaultProps = {\n    required: false,\n    requiredInput: false,\n    value: \"\",\n    subLabel: \"\"\n};\n\n;// ../src/views/Componenets/FormElements/TextInput.tsx\n\n\n\n\n\n\nvar TextInput = function (props) {\n    var privateProps = __assign(__assign({}, TextInput_defaultProps), props);\n    var label = privateProps.label, subLabel = privateProps.subLabel, handleChange = privateProps.handleChange, containerClass = privateProps.containerClass, extention = privateProps.extention, optionalExtenstion = privateProps.optionalExtenstion, requiredInput = privateProps.requiredInput, requiredPattern = privateProps.requiredPattern, value = privateProps.value, subValue = privateProps.subValue;\n    var _a = useFormContext(), register = _a.register, errors = _a.errors;\n    var getAriaLabel = function (label) {\n        switch (label) {\n            case \"TELEPHONE_FORMAT\":\n            case \"PREFERED_PHONE_FORMAT\":\n            case \"PREFERED_TEXT_MESSAGE_FORMAT\":\n            case \"Phone_FORMAT\":\n            case \"TextMessage_FORMAT\":\n                return Localization.getLocalizedString(\"TELEPHONE_FORMAT_ARIA\");\n            default:\n                return Localization.getLocalizedString(label);\n        }\n    };\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock flexCol-xs margin-15-bottom flexWrap \".concat(containerClass) },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { htmlFor: \"additionalPhoneNumber\", className: \"installation-form-label \".concat(requiredInput ? \"form-required\" : \"\", \" \").concat(errors && errors[label] ? \"error\" : \"\") },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtBold block\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: label })),\n            subLabel ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtItalic block txtNormal\", \"aria-label\": getAriaLabel(subLabel) },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: subLabel })) : null),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol relative \".concat(errors && errors[label] ? \"has-error\" : \"\") },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"topArrow text-left hide\", \"aria-hidden\": \"true\" }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"input\", { type: \"text\", ref: register({ required: requiredInput, pattern: requiredPattern }), className: \"brf3-virgin-form-input form-control\", id: label, name: label, title: label, defaultValue: value, onBlur: handleChange, onChange: function (e) { return handleChange(e); } }),\n            errors && errors[label] ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"error margin-5-top\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon icon-warning margin-15-right\", \"aria-hidden\": true },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"volt-icon path1\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"volt-icon path2\" })),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtSize12\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: errors[label].type !== \"pattern\" ? \"INLINE_ERROR_required\" : \"INLINE_ERROR_\".concat(label, \"_pattern\") }))) : null),\n        extention ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol brf3-virgin-form-subInput fill-sm\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock flexCol-xs\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { htmlFor: \"extension\", className: \"installation-form-label\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtBold block\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: extention })),\n                    optionalExtenstion ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtItalic block txtNormal\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"OPTIONAL_LABEL\" })) : null),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"input\", { type: \"text\", ref: register, className: \"brf3-virgin-form-input form-control\", id: extention, name: extention, title: extention, maxLength: 10, defaultValue: subValue, onBlur: handleChange, onChange: function (e) { return handleChange(e); } })))) : null));\n};\nvar TextInput_defaultProps = {\n    requiredInput: false,\n    requiredPattern: /.*/i,\n    containerClass: \"\",\n    value: \"\",\n    subValue: \"\"\n};\n\n;// ../src/views/Componenets/FormElements/Fieldset.tsx\n\n\nvar Legend = function (_a) {\n    var legend = _a.legend, required = _a.required, accessibleLegend = _a.accessibleLegend, legendAdditionalClass = _a.legendAdditionalClass;\n    return legend ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"legend\", { className: \"installation-form-label \".concat(required ? \"form-required\" : \"\", \" \").concat(accessibleLegend ? \"sr-only\" : \"\", \" \").concat(legendAdditionalClass) },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: legend })) : null;\n};\nvar Fieldset = function (_a) {\n    var className = _a.className, children = _a.children, legend = _a.legend, accessibleLegend = _a.accessibleLegend, legendAdditionalClass = _a.legendAdditionalClass, required = _a.required, additionalClass = _a.additionalClass;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"fieldset\", { className: \"margin-15-bottom \".concat(className) }, accessibleLegend ?\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Legend, { legend: legend, required: required, accessibleLegend: accessibleLegend, legendAdditionalClass: legendAdditionalClass }),\n            children) :\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock flexCol-xs \".concat(additionalClass) },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Legend, { legend: legend, required: required, accessibleLegend: accessibleLegend, legendAdditionalClass: legendAdditionalClass }),\n            children));\n};\n\n;// ../src/views/Componenets/FormElements/index.ts\n\n\n\n\n\n\n;// ../src/views/Componenets/Header/Banner.tsx\n\n\n\n\nvar EBannerIcons;\n(function (EBannerIcons) {\n    EBannerIcons[\"ERROR\"] = \"icon-warning\";\n    EBannerIcons[\"NOTE\"] = \"icon-info\";\n    EBannerIcons[\"VALIDATION\"] = \"icon-Big_check_confirm\";\n    EBannerIcons[\"INFO\"] = \"icon-BIG_WARNING\";\n})(EBannerIcons || (EBannerIcons = {}));\nvar Banner = function (props) {\n    var privateProps = __assign(__assign({}, Banner_defaultProps), props);\n    var iconType = privateProps.iconType, heading = privateProps.heading, message = privateProps.message, messages = privateProps.messages, iconSizeCSS = privateProps.iconSizeCSS;\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Container, null,\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Panel, { className: \"flexBlock pad-20-left pad-20-right pad-25-top pad-25-bottom margin-15-bottom txtBlack\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon \".concat(iconType, \" \").concat(iconSizeCSS, \" txtSize36\"), \"aria-hidden\": true },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon path1\" }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon path2\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { id: \"IstallationMessageBanner\", className: \"flexCol pad-15-left content-width valign-top pad-0-xs\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"h4\", { className: \"virginUltraReg txtSize18 txtDarkGrey1 no-margin-top txtUppercase\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: heading })),\n                message ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"txtSize14 txtGray4A sans-serif no-margin\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: message })) : null,\n                messages ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"ul\", null, messages && messages.map(function (message) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"li\", { className: \"error\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"a\", { id: \"message_\".concat(message.id), href: \"#\".concat(message.id), className: \"txtRed txtBold txtUnderline\", title: message.id },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: message.id })),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtDarkGrey\" },\n                        \"\\u00A0-\\u00A0\",\n                        message.error === \"required\" ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"INLINE_ERROR_required\" }) : external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"INLINE_ERROR_\" + message.id + \"_\" + message.error }))); })) : null))));\n};\nvar Banner_defaultProps = {\n    iconType: EBannerIcons.INFO,\n    iconSizeCSS: \"txtSize36\"\n};\n\n;// ../src/views/Componenets/Header/Heading.tsx\n\n\n\n\nvar HeadingTags;\n(function (HeadingTags) {\n    HeadingTags[\"H1\"] = \"h1\";\n    HeadingTags[\"H2\"] = \"h2\";\n    HeadingTags[\"H3\"] = \"h3\";\n    HeadingTags[\"H4\"] = \"h4\";\n    HeadingTags[\"H5\"] = \"h5\";\n    HeadingTags[\"H6\"] = \"h6\";\n})(HeadingTags || (HeadingTags = {}));\nvar Heading = function (props) {\n    var privateProps = __assign(__assign({}, Heading_defaultProps), props);\n    var tag = privateProps.tag, additionalClass = privateProps.additionalClass, content = privateProps.content, description = privateProps.description;\n    var Tag = tag || \"h2\";\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Tag, { className: \"virginUltra txtBlack txtCapital noMargin \".concat(additionalClass) },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: content })),\n        description ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer10 col-xs-12 clear\" }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"noMargin\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: description }))) : null));\n};\nvar Heading_defaultProps = {\n    additionalClass: \"\",\n    description: \"\"\n};\n\n;// ../src/views/Componenets/Header/Header.tsx\n\n\n\n\n\n\nvar Header = (function (_super) {\n    __extends(Header, _super);\n    function Header(props) {\n        var _this = _super.call(this, props) || this;\n        _this.headingProps = {\n            tag: HeadingTags.H2,\n            classNames: \"txtSize28 txtSize24-xs\",\n            content: \"INSTALLATION_PAGE_HEADING\",\n        };\n        return _this;\n    }\n    Header.prototype.render = function () {\n        return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Container, null,\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.BRF3Container, null,\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer5 flex col-12\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Heading, __assign({}, this.headingProps)),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer25 flex col-12 clear\" }))),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Banner, { iconType: EBannerIcons.INFO, heading: \"INSTALLATION_HEADING\", message: \"INSTALLATION_MESSAGE\" }),\n            Object.keys(this.props.errors).length ? (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Banner, { iconType: EBannerIcons.ERROR, heading: \"ERRORS_HEADING\", messages: getMessagesList(this.props.errors) })) : null));\n    };\n    return Header;\n}(external_root_React_commonjs2_react_commonjs_react_amd_react_.PureComponent));\n\n\n;// ../src/views/Componenets/Header/index.ts\n\n\n\n\n;// ../src/views/Componenets/ContactInformation/ContactInformation.tsx\n\n\n\n\n\n\n\n\nvar ContactInformation = function () {\n    var _a, _b;\n    var contactInformation = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.useSelector)(function (state) { return state === null || state === void 0 ? void 0 : state.contactInformation; });\n    var additionalDetails = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.useSelector)(function (state) { return state === null || state === void 0 ? void 0 : state.additionalDetails; });\n    var _c = __read(external_root_React_commonjs2_react_commonjs_react_amd_react_.useState(EContactMethod.PHONE), 2), contactMethod = _c[0], setContactMethod = _c[1];\n    var setValue = useFormContext().setValue;\n    var handleChange = function (e) {\n        var _a = e.target, value = _a.value, name = _a.name;\n        switch (value) {\n            case EContactMethod.PHONE:\n            case EContactMethod.EMAIL:\n            case EContactMethod.TEXT_MESSAGE:\n                setContactMethod(value);\n                break;\n            default:\n                break;\n        }\n        switch (name) {\n            case EContactMethod.PHONE + \"_LABEL\":\n            case EContactMethod.TEXT_MESSAGE + \"_LABEL\":\n            case \"ADDITIONAL_PHONE_NUMBER\":\n                setValue(name, autoFormat(value), true);\n                break;\n            case EContactMethod.PHONE + \"_EXT\":\n            case \"ADDITIONAL_PHONE_EXT\":\n                setValue(name, filterNumbers(value), true);\n                break;\n            case \"SUPERINTENDANT_PHONE\":\n                setValue(name, autoFormat(value), true);\n                break;\n            case EContactMethod.EMAIL + \"_LABEL\":\n                setValue(name, value, true);\n                break;\n            default:\n                break;\n        }\n    };\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {\n        setContactMethod((contactInformation === null || contactInformation === void 0 ? void 0 : contactInformation.preferredContactMethod) ? contactInformation.preferredContactMethod : EContactMethod.PHONE);\n    }, [contactInformation]);\n    var headingProps = {\n        tag: HeadingTags.H2,\n        additionalClass: \"txtSize22 txtSize24-xs\",\n        content: \"CONTACT_INFORMATION\"\n    };\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"margin-30-bottom\", id: \"section2\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Heading, __assign({}, headingProps)),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer10 visible-xs\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"pad-25-top no-pad-xs\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Fieldset, { legend: \"PREFERED_METHOD_OF_CONTACT\", required: true, additionalClass: \"flexWrap\", accessibleLegend: false },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol lineHeight18\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer15 visible-xs\" }),\n                    mapEnum(EContactMethod, function (item) {\n                        return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(RadioBtn, { label: \"PREFERED_METHOD_OF_CONTACT\", value: item, handleChange: handleChange, checked: item === contactMethod });\n                    })),\n                mapEnum(EContactMethod, function (item) {\n                    var _a;\n                    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { requiredInput: contactMethod === item, label: item + \"_LABEL\", containerClass: \"sub-option flex-wrap \".concat(item === contactMethod ? \"show\" : \"hide\"), subLabel: item + \"_FORMAT\", extention: item === EContactMethod.PHONE ? item + \"_EXT\" : false, optionalExtenstion: true, requiredPattern: item === EContactMethod.EMAIL ? emailRegex : formattedPhoneRegex, value: getPrimaryValue(item, contactInformation), subValue: (_a = contactInformation === null || contactInformation === void 0 ? void 0 : contactInformation.primaryPhone) === null || _a === void 0 ? void 0 : _a.phoneExtension, handleChange: function (e) { return handleChange(e); } });\n                })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Fieldset, { legend: \"ADDITIONAL_PHONE_NUMBER\", required: false, accessibleLegend: true },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { requiredInput: false, label: \"ADDITIONAL_PHONE_NUMBER\", subLabel: \"TELEPHONE_FORMAT\", extention: \"ADDITIONAL_PHONE_EXT\", optionalExtenstion: true, requiredPattern: formattedPhoneRegex, value: (_a = contactInformation === null || contactInformation === void 0 ? void 0 : contactInformation.additionalPhone) === null || _a === void 0 ? void 0 : _a.phoneNumber, subValue: (_b = contactInformation === null || contactInformation === void 0 ? void 0 : contactInformation.additionalPhone) === null || _b === void 0 ? void 0 : _b.phoneExtension, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { label: \"APPARTMENT\", value: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.apartment, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { label: \"ENTRY_CODE\", value: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.entryCode, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { label: \"SUPERINTENDANT_NAME\", value: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.superintendantName, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { label: \"SUPERINTENDANT_PHONE\", requiredPattern: formattedPhoneRegex, value: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.superintendantPhone, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Checkbox, { label: \"INFORMED_SUPERINTENDANT\", value: \"YES\", checked: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.informedSuperintendant, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextArea, { label: \"SPECIAL_INSTRUCTIONS\", subLabel: \"SPECIAL_INSTRUCTIONS_SUBLABEL\", value: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.specialInstructions, maxLength: 200, handleChange: function (e) { return handleChange(e); } })))));\n};\n\n;// ../src/views/Componenets/ContactInformation/index.ts\n\n\n;// ../src/views/Componenets/Installation/DateAndTime/DateAndTime.tsx\n\n\n\nvar DateAndTime = external_root_React_commonjs2_react_commonjs_react_amd_react_.memo(function (props) {\n    var _a;\n    var handleChange = props.handleChange, preferredDate = props.preferredDate, checked = props.checked, register = useFormContext().register;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { id: \"dateAndTime\" + preferredDate.date, className: \"graphical_ctrl pointer ctrl_radioBtn margin-10-bottom\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"input\", { type: \"radio\", ref: register({ required: true }), id: \"timeOption\" + preferredDate.date, name: \"dateAndTime\", value: JSON.stringify(preferredDate), onChange: function (e) { return handleChange(e); }, checked: checked.date === preferredDate.date }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { className: \"block no-margin\", htmlFor: \"timeOption\" + preferredDate.date }, Boolean(preferredDate.date) ?\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedDate, { value: preferredDate.date, year: \"numeric\", weekday: \"long\", month: \"long\", day: \"2-digit\", timeZone: \"UTC\" }) :\n                \"No Appointment Details\"),\n            Boolean(preferredDate.timeSlots.length) ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtNormal block\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: (_a = preferredDate.timeSlots.find(function (item) { return item.isAvailable; })) === null || _a === void 0 ? void 0 : _a.intervalType })) : null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"ctrl_element\" })));\n});\n\n;// ../src/views/Componenets/Installation/DateAndTime/TimeSlots.tsx\n\n\n\n\n\n\nvar TimeSlots = (function (_super) {\n    __extends(TimeSlots, _super);\n    function TimeSlots(props) {\n        return _super.call(this, props) || this;\n    }\n    TimeSlots.prototype.componentDidMount = function () {\n        this.props.initSlickSlider();\n    };\n    TimeSlots.prototype.render = function () {\n        var _a = this.props, availableDates = _a.availableDates, selectDate = _a.selectDate, selectedDateTime = _a.selectedDateTime;\n        return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock margin-15-bottom sub-option relative timeslot-picker\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"select-timeslot fill\" }, availableDates && availableDates.map(function (day, dayIndex) {\n                return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: day.timeSlots[0].intervalType === EDuration.AllDay ? \"allDayContainer\" : \"day-container\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { htmlFor: \"dayIndex_\" + dayIndex, className: \"virginUltra sans-serif-xs txtBold-xs txtSize16 txtBlack lineHeight1-3 margin-15-bottom\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedDate, { value: stripTimeBit(day.date), weekday: \"long\", timeZone: \"UTC\" }),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"br\", { className: \"hidden-m\" }),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"d-sm-none d-md-none d-lg-none d-xl-none\" }, \", \"),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedDate, { value: stripTimeBit(day.date), year: \"numeric\", month: \"short\", day: \"2-digit\", timeZone: \"UTC\" })),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"ul\", { className: \"noMargin list-unstyled timeItem\", \"aria-labelledby\": \"mondayList\" }, day.timeSlots.map(function (timeSlot) {\n                            var selectedInterval = selectedDateTime.timeSlots[0].intervalType === timeSlot.intervalType && selectedDateTime.date === day.date;\n                            return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"li\", { className: \"txtBlue \".concat(selectedInterval ? \"selected\" : \"\") },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"slot_\".concat(timeSlot.intervalType), onClick: function (e) { return selectDate(e, day.date, timeSlot); }, className: \"btn btn-link \".concat(timeSlot.intervalType === EDuration.AllDay ? \"flexCol flexJustify\" : \"\", \" \").concat(timeSlot.isAvailable ? \"\" : \"disabled\", \" \").concat(timeSlot.isSelected ? \"selected\" : \"\"), tabIndex: 0 },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: timeSlot.intervalType })));\n                        }))));\n            })));\n    };\n    TimeSlots.displayName = \"TimeSlots\";\n    return TimeSlots;\n}(external_root_React_commonjs2_react_commonjs_react_amd_react_.Component));\n\n\n;// ../src/views/Componenets/Installation/DateAndTime/index.ts\n\n\n\n;// ../src/views/Componenets/Installation/Installation.tsx\n\n\n\n\n\n\n\n\nvar Visible = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible;\nvar Component = (function (_super) {\n    __extends(Component, _super);\n    function Component(props) {\n        var _this = _super.call(this, props) || this;\n        _this.handleChange = function (e) {\n            var value = e.target.value;\n            switch (value) {\n                case \"OTHER\":\n                    _this.setState({\n                        showTimeSlots: true,\n                    });\n                    break;\n                default:\n                    _this.setState({\n                        showTimeSlots: false,\n                        selectedDateTime: JSON.parse(value)\n                    });\n                    break;\n            }\n        };\n        _this.selectDate = function (e, day, interval) {\n            e.preventDefault();\n            var newPreferedDates = __spreadArray([], __read(_this.state.preferredDates), false);\n            if (_this.state.preferredDates[0].date === day &&\n                _this.state.preferredDates[0].timeSlots[0].intervalType === interval.intervalType) {\n                _this.setState({\n                    preferredDates: _this.state.preferredDates,\n                    selectedDateTime: _this.state.preferredDates[0],\n                    showTimeSlots: false,\n                    showOther: false\n                });\n            }\n            else {\n                newPreferedDates[1] = { date: day, timeSlots: [__assign(__assign({}, interval), { isSelected: true })] };\n                _this.setState({\n                    preferredDates: newPreferedDates,\n                    selectedDateTime: newPreferedDates[1],\n                    showTimeSlots: false,\n                    showOther: false\n                });\n            }\n        };\n        _this.changeBtn = function (e) {\n            e.preventDefault();\n            _this.setState({\n                showOther: true,\n                showTimeSlots: true,\n                preferredDates: [_this.state.preferredDates[0]]\n            });\n        };\n        _this.state = {\n            showTimeSlots: false,\n            selectedDateTime: null,\n            preferredDates: [],\n            showOther: true\n        };\n        _this.handleChange.bind(_this);\n        _this.changeBtn.bind(_this);\n        return _this;\n    }\n    Component.prototype.componentDidUpdate = function (props) {\n        if (this.props.availableDates && this.props.availableDates.length && JSON.stringify(this.props.availableDates) !== JSON.stringify(props.availableDates)) {\n            var selectedDate = getSelectedDate(this.props.availableDates);\n            this.setState({\n                preferredDates: selectedDate,\n                selectedDateTime: selectedDate[0].date ? selectedDate[0] : null,\n                showOther: selectedDate.length > 1 ? false : true\n            });\n        }\n    };\n    Component.prototype.render = function () {\n        var _this = this;\n        var _a = this.props, installationAddress = _a.installationAddress, availableDates = _a.availableDates, initSlickSlider = _a.initSlickSlider;\n        var _b = this.state, showTimeSlots = _b.showTimeSlots, selectedDateTime = _b.selectedDateTime, showOther = _b.showOther, preferredDates = _b.preferredDates;\n        var headingProps = {\n            tag: HeadingTags.H2,\n            additionalClass: \"txtSize22 txtSize24-xs\",\n            content: \"INSTALLATION_DETAILS\",\n            description: \"INSTALLATION_DETAILS_DESC\"\n        };\n        return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"margin-30-bottom\", id: \"section1\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Heading, __assign({}, headingProps)),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer10 flex col-12 clear\" }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"noMargin txtItalic\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"REQUIRED_INFO_FLAG\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"pad-15-top\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Fieldset, { legend: \"DATE_AND_TIME_LABEL\", required: true, accessibleLegend: false, additionalClass: \"flex-wrap\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer10 visible-xs\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol lineHeight18\" },\n                        preferredDates && preferredDates.length && preferredDates.map(function (date) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(DateAndTime, { handleChange: _this.handleChange, preferredDate: date, checked: showTimeSlots || selectedDateTime }); }),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: showOther, placeholder: external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"pad-35-left relative changeBtn\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"CHANGE_BTN\", className: \"btn btn-link pad-0 txtSize14 txtUnderline txtVirginBlue\", onClick: function (e) { return _this.changeBtn(e); } }, \"Change\")) },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(RadioBtn, { handleChange: this.handleChange, requiredInput: true, checked: showTimeSlots, label: \"dateAndTime\", value: \"OTHER\" }))),\n                    showTimeSlots ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TimeSlots, { selectDate: this.selectDate, availableDates: availableDates, initSlickSlider: initSlickSlider, selectedDateTime: selectedDateTime }) : null),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: Boolean(selectedDateTime) }, selectedDateTime && selectedDateTime !== \"OTHER\" ?\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Fieldset, { legend: \"ESTIMATED_DURATION\", required: false, accessibleLegend: false },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"block\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: selectedDateTime.timeSlots[0].duration })),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"block\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"ARRIVAL_OF_TECHNICIAN\" })))) : null),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: Boolean(installationAddress) },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Fieldset, { legend: \"SHIPPING_INSTALLATION_ADDRESS\", required: false, accessibleLegend: false },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"block\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"apartmentNumber\", false) },\n                                    (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"apartmentNumber\", \"\"),\n                                    \"\\u00A0-\\u00A0\"),\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"address1\", \"\"),\n                                \"\\u00A0\",\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"address2\", \"\"),\n                                \"\\u00A0\",\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"streetType\", \"\"),\n                                \",\\u00A0\",\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"city\", \"\"),\n                                \",\\u00A0\",\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"province\", \"\"),\n                                \",\\u00A0\",\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"postalCode\", \"\")),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"margin-10-top\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: \"CONTACT_US_NOTE\" }))))))));\n    };\n    return Component;\n}(external_root_React_commonjs2_react_commonjs_react_amd_react_.Component));\n\n\n;// ../src/views/Componenets/Installation/index.ts\n\n\n\nvar Installation = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {\n    var installationAddress = _a.installationAddress, availableDates = _a.availableDates, duration = _a.duration;\n    return ({ installationAddress: installationAddress, availableDates: availableDates, duration: duration });\n}, function (dispatch) { return ({\n    initSlickSlider: function () { return dispatch(initSlickSlider()); }\n}); })(Component);\n\n;// ../src/views/Componenets/index.ts\n\n\n\n\n\n;// ../src/views/Form.tsx\n\n\n\n\n\n\nvar _submitForm = null;\nvar Form = function (props) {\n    var submitRef = external_root_React_commonjs2_react_commonjs_react_amd_react_.useRef(null);\n    var handleSubmit = useFormContext().handleSubmit;\n    var dispatch = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.useDispatch)();\n    _submitForm = function () {\n        submitRef.current.click();\n    };\n    var customSubmit = function (e) { return __awaiter(void 0, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            e.preventDefault();\n            handleSubmit(function (data) {\n                dispatch(setAppointment(data));\n            })(e);\n            return [2];\n        });\n    }); };\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"form\", { id: \"AppointmentForm\", onSubmit: customSubmit },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer45 hidden-m\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer20 d-block d-sm-none\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Installation, null),\n        \" \",\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ContactInformation, null),\n        \" \",\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { ref: submitRef, type: \"submit\", \"aria-hidden\": \"true\", style: { display: \"none\" } })));\n};\nForm.useSubmitRef = function () { return _submitForm; };\n/* harmony default export */ var views_Form = (Form);\n\n;// ../src/Pipe.ts\n\n\n\n\nvar BasePipe = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BasePipe;\nvar Pipe = (function (_super) {\n    __extends(Pipe, _super);\n    function Pipe(arg) {\n        var _this = _super.call(this, arg) || this;\n        Pipe.instance = _this;\n        return _this;\n    }\n    Pipe.Subscriptions = function (store) {\n        var _a;\n        return _a = {},\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.onContinue.toString()] = function (_a) {\n                var ref = views_Form.useSubmitRef();\n                ref && ref();\n                store.dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.RENDERED));\n            },\n            _a;\n    };\n    return Pipe;\n}(BasePipe));\n\n\n;// ../src/views/index.tsx\n\n\n\n\n\n\nvar RestrictionModal = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.RestrictionModal;\nvar widgetRenderComplete = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.widgetRenderComplete;\nvar Application = function (props) {\n    var dispatch = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.useDispatch)();\n    var errors = useFormContext().errors;\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {\n        dispatch(widgetRenderComplete(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.APPOINTMENT));\n    }, []);\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"main\", { id: \"mainContent\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"flex spacer30 col-12\", \"aria-hidden\": \"true\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(RestrictionModal, { id: \"APPOINTMENT_RESTRICTION_MODAL\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Header, { errors: errors }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Container, null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Panel, { className: \"pad-25-left pad-25-right clearfix\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(views_Form, null))));\n};\n\n;// ../src/App.tsx\n\n\n\n\n\nvar ApplicationRoot = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.ApplicationRoot;\nvar App = function (props) {\n    var methods = useForm();\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ApplicationRoot, null,\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(FormContext, __assign({}, methods),\n            \" \",\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Application, null))));\n};\n\n;// ../src/Widget.tsx\n\n\n\n\n\n\n\n\n\nvar setWidgetProps = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetProps, Widget_setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus;\nvar StoreProvider = external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.Provider;\nvar WidgetContainer = (function (_super) {\n    __extends(WidgetContainer, _super);\n    function WidgetContainer(store, params, config, pipe) {\n        var _this = _super.call(this) || this;\n        _this.store = store;\n        _this.params = params;\n        _this.config = config;\n        _this.pipe = pipe;\n        return _this;\n    }\n    WidgetContainer.prototype.init = function () {\n        this.pipe.subscribe(Pipe.Subscriptions(this.store));\n        this.store.dispatch(setWidgetProps(this.config));\n        this.store.dispatch(setWidgetProps(this.params.props));\n        this.store.dispatch(Widget_setWidgetStatus(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT));\n    };\n    WidgetContainer.prototype.destroy = function () {\n        this.pipe.unsubscribe();\n        this.store.destroy();\n    };\n    WidgetContainer.prototype.render = function (root) {\n        var store = this.store;\n        root.render(external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ContextProvider, { value: { config: this.config } },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(StoreProvider, { store: store },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(App, null))));\n    };\n    WidgetContainer = __decorate([\n        (0,external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Widget)({ namespace: \"Ordering\" }),\n        __metadata(\"design:paramtypes\", [Store, external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ParamsProvider, Config, Pipe])\n    ], WidgetContainer);\n    return WidgetContainer;\n}(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ViewWidget));\n/* harmony default export */ var Widget = (WidgetContainer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../src/Widget.tsx\n\n}");

/***/ }),

/***/ "bwtk":
/*!**********************************************************************************!*\
  !*** external {"root":"bwtk","commonjs2":"bwtk","commonjs":"bwtk","amd":"bwtk"} ***!
  \**********************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_bwtk__;

/***/ }),

/***/ "omf-changepackage-components":
/*!********************************************************************************************************************************************************************************!*\
  !*** external {"root":"OMFChangepackageComponents","commonjs2":"omf-changepackage-components","commonjs":"omf-changepackage-components","amd":"omf-changepackage-components"} ***!
  \********************************************************************************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_omf_changepackage_components__;

/***/ }),

/***/ "react":
/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

/***/ }),

/***/ "react-intl":
/*!*********************************************************************************************************!*\
  !*** external {"root":"ReactIntl","commonjs2":"react-intl","commonjs":"react-intl","amd":"react-intl"} ***!
  \*********************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_intl__;

/***/ }),

/***/ "react-redux":
/*!*************************************************************************************************************!*\
  !*** external {"root":"ReactRedux","commonjs2":"react-redux","commonjs":"react-redux","amd":"react-redux"} ***!
  \*************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_redux__;

/***/ }),

/***/ "redux":
/*!**************************************************************************************!*\
  !*** external {"root":"Redux","commonjs2":"redux","commonjs":"redux","amd":"redux"} ***!
  \**************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux__;

/***/ }),

/***/ "redux-actions":
/*!*********************************************************************************************************************!*\
  !*** external {"root":"ReduxActions","commonjs2":"redux-actions","commonjs":"redux-actions","amd":"redux-actions"} ***!
  \*********************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_actions__;

/***/ }),

/***/ "redux-observable":
/*!*********************************************************************************************************************************!*\
  !*** external {"root":"ReduxObservable","commonjs2":"redux-observable","commonjs":"redux-observable","amd":"redux-observable"} ***!
  \*********************************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_observable__;

/***/ }),

/***/ "rxjs":
/*!**********************************************************************************!*\
  !*** external {"root":"rxjs","commonjs2":"rxjs","commonjs":"rxjs","amd":"rxjs"} ***!
  \**********************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_rxjs__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("../src/Widget.tsx");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});